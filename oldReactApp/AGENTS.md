# Repository Instructions

## Important
- Run `pnpm install` before starting to work and wait for it to complete (be patient as it can take a while).
- If packages install is not working, stop the task immediately and inform the user.

## General Workflow
- Follow a **Test-Driven Development** approach.
- After each iteration or change, run `pnpm run lint` and `pnpm test` (create the commands if not exist).
- Both commands must succeed before committing.
- Keep commits focused and describe the functional change.

## Testing Guidelines
- Tests use **Vitest** with jest-extended matchers.
- Use `pnpm test` to run them.
- Structure tests with the sections:
  ```ts
  // Arrange
  // Act
  // Assert
  ```
- Only perform **one** action in the `// Act` section.
- Use sociable tests that exercise use cases (command or query handlers).
- These act as acceptance tests; the domain is never tested directly.
- Avoid overly technical tests and solitary unit tests.
- Do **not** perform multiple state updates in one test; setup must be manual.
- `describe` blocks should use `describe("When ...")` to give context.
- Each `it` statement must start with `should` and stay close to the functional behaviour.
- Never leave `.only` or `.skip` in committed tests.

## Project Architecture
- The project follows **Clean Architecture + DDD + CQRS + TDD** organized by **Bounded Contexts**:
    - **Structure**: `src/client/[BoundedContext]` and `src/server/[BoundedContext]`
    - **Bounded Contexts examples**: `Authentication`, `PokerPlanning`, `Shared`
    - **Layers**: `domain`, `application`, `infrastructure`, `presentation`, `specs`
    - **ESLint boundary rules** enforce these constraints

## Key Patterns
- **CQRS**: Commands and Queries with dedicated handlers
- **Repository Pattern**: Interfaces in application, implementations in infrastructure
- **Presenter Pattern**: For view models and display logic
- **Domain Entities**: With factory methods, snapshots, and business logic
- **Value Objects**: Immutable objects representing domain concepts

## File Naming Conventions
- **Commands**: `[Action]Command.ts`, `[Action]CommandHandler.ts`
- **Queries**: `[Action]Query.ts`, `[Action]QueryHandler.ts`
- **Repositories**: `[Entity]Repository.ts` (interface), `Convex[Entity]Repository.ts`, `InMemory[Entity]Repository.ts`
- **Presenters**: `[Action]Presenter.ts` (interface), `[Action]WebPresenter.ts`
- **Tests**: `[action].spec.ts`
- **Domain**: `[Entity].ts`, `[ValueObject].ts`

## Import Conventions
- Import from interfaces, not implementations
- Follow dependency direction: domain ← application ← infrastructure/presentation

## Tools
- **Linting**: `pnpm run lint` (TypeScript + ESLint with boundary rules)
- **Testing**: `pnpm test` (Vitest)
- **Coverage**: `pnpm run test:coverage`
- **Development**: `pnpm run dev:all` (Next.js + Convex)

## Frontend Specific
- **Framework**: Next.js 15 with React 19
- **UI**: Radix UI components and themes
- **State**: Zustand with clean architecture patterns
- **Internationalization**: Supports `en` and `fr` locales
- **Styling**: Tailwind CSS
- **Components**: Apply the humble object pattern. Move all logic outside of
  React components by relying on async thunks for use cases and selectors for
  querying data. Use hooks with no logic as the glue between components and
  use cases.

## Commit Requirements
- Keep the working directory clean before committing
- Include relevant tests for new features or fixes
- Ensure `pnpm run lint` and `pnpm test` pass before every commit
- Follow conventional commit messages describing functional changes
- Branch names should be descriptive and related to the task or feature in english


## Directory Overview
- **src** contains all bounded contexts organized under `client` and `server`:
    - `src/client/[BoundedContext]`
        - `application`
        - `domain`
        - `infrastructure`
        - `specs`
    - `src/server/[BoundedContext]`
        - `application`
        - `domain`
        - `infrastructure`
        - `presentation`
        - `specs`

Each bounded context repeats this layered structure. The complete directory tree
for the `src` folder is provided below as a reference so you can see the
contents of every `application`, `domain`, `infrastructure` and `presentation`
folder.

## Example (you will not need all of this)
```
src
├── client
│   ├── Authentication
│   │   ├── application
│   │   │   ├── commands
│   │   │   │   └── signIn
│   │   │   │       ├── signIn.ts
│   │   │   │       └── signInRequest.ts
│   │   │   └── queries
│   │   │       ├── getAuthError
│   │   │       │   └── getAuthError.ts
│   │   │       └── isSigningIn
│   │   │           └── isSigningIn.ts
│   │   ├── domain
│   │   │   └── Auth
│   │   │       ├── authEvents.ts
│   │   │       └── authReducer.ts
│   │   ├── infrastructure
│   │   │   ├── components
│   │   │   │   └── app
│   │   │   │       └── Auth
│   │   │   │           └── SignIn
│   │   │   │               ├── SignInButton
│   │   │   │               │   └── SignInButton.tsx
│   │   │   │               └── SignInForm
│   │   │   │                   ├── SignInForm.tsx
│   │   │   │                   └── useSignInForm.tsx
│   │   │   └── pages
│   │   │       └── Auth
│   │   │           ├── AccessDeniedPage
│   │   │           │   └── AccessDeniedPage.tsx
│   │   │           └── SignInPage
│   │   │               └── SignInPage.tsx
│   │   └── specs
│   │       └── unit
│   │           └── commands
│   │               └── signIn.spec.ts
│   ├── PokerPlanning
│   │   ├── application
│   │   │   ├── commands
│   │   │   │   ├── createSession
│   │   │   │   │   ├── createSession.ts
│   │   │   │   │   └── createSessionRequest.ts
│   │   │   │   ├── estimateStory
│   │   │   │   │   ├── estimateStory.ts
│   │   │   │   │   └── estimateStoryRequest.ts
│   │   │   └── queries
│   │   │       ├── getCurrentSession
│   │   │       │   └── getCurrentSession.ts
│   │   ├── domain
│   │   │   └── PokerPlanningSession
│   │   │       ├── PokerPlanningSession.ts
│   │   │       └── PokerPlanningSessionEvents.ts
│   │   ├── infrastructure
│   │   │   └── pages
│   │   │       └── CatalogManagementPage
│   │   │           └── CatalogManagementPage.tsx
│   │   └── specs
│   │       └── unit
│   │           └── commands
│   │               └── createSession.spec.ts
│   └── Shared
│       ├── components
│       │   ├── MySharedComponent
│       │       └── MySharedComponent.tsx
│       ├── helpers
│       │   ├── MyHelper
│       │   │   └── MyHelper.ts
│       ├── hooks
│       │   ├── myHook
│       │   │   └── myHook.ts
│       ├── layouts
│       │   └── RootLayout
│       │       └── RootLayout.tsx
│       ├── providers
│       │   ├── ConvexClientProvider
│       │   │   ├── ConvexClient.ts
│       │   │   └── ConvexClientProvider.tsx
│       │   └── ToastProvider
│       │       ├── ToastContext.tsx
│       │       ├── ToastProvider.tsx
│       │       └── useToast.tsx
│       ├── services
│       │   ├── Location
│       │   │   ├── FakeLocationService.ts
│       │   │   ├── LocationService.ts
│       │   │   └── RealLocationService.ts
│       │   └── Timer
│       │       ├── FakeTimerService.ts
│       │       ├── RealTimerService.ts
│       │       └── TimerService.ts
│       ├── store
│       │   ├── appStore
│       │   │   ├── appDispatch.ts
│       │   │   ├── rootInitialState.ts
│       │   │   ├── rootReducers.ts
│       │   │   ├── rootState.ts
│       │   │   └── thunkExtra.ts
│       └── testing
│           └── store
│               └── createTestingStore.ts
└── server
    ├── Authentication
    │   ├── application
    │   │   ├── ports
    │   │   │   ├── AppUserRepository.ts
    │   │   │   ├── AuthenticationGateway.ts
    │   │   │   ├── CurrentUserRepository.ts
    │   │   │   └── GetCurrentUserPresenter.ts
    │   │   └── queries
    │   │       └── GetCurrentUser
    │   │           └── GetCurrentUserQueryHandler.ts
    │   ├── domain
    │   │   ├── AppUser
    │   │   │   ├── AppUser.ts
    │   │   │   └── valueObjects
    │   │   │       └── AppUserId.ts
    │   │   └── User
    │   │       ├── CurrentUser.ts
    │   │       └── errors
    │   │           ├── UserNotAuthenticatedError.ts
    │   │           └── UserNotRegisteredError.ts
    │   ├── infrastructure
    │   │   ├── gateways
    │   │   │   └── AuthenticationGateway
    │   │   │       └── ConvexAuthenticationGateway.ts
    │   │   └── repositories
    │   │       ├── AppUser
    │   │       │   ├── ConvexAppUserRepository.ts
    │   │       │   └── InMemoryAppUserRepository.ts
    │   │       └── CurrentUser
    │   │           ├── ConvexCurrentUserRepository.ts
    │   │           ├── InMemoryCurrentUserRepository.ts
    │   │           └── InMemoryFailingCurrentUserRepository.ts
    │   ├── presentation
    │   │   ├── presenters
    │   │   │   └── GetCurrentUserWebPresenter.ts
    │   │   └── viewModels
    │   │       └── GetCurrentUserViewModel.ts
    │   └── specs
    │       ├── helpers
    │       │   ├── createAppUsers.ts
    │       │   ├── createFakeUsers.ts
    │       │   ├── dtos
    │       │   │   └── UserDTO.ts
    │       │   ├── fakes
    │       │   │   └── fakeUsers.ts
    │       │   └── getAppUser.ts
    │       ├── integration
    │       │   └── gateways
    │       │       └── ConvexAuthenticationGateway.spec.ts
    │       └── unit
    │           └── queries
    │               └── GetCurrentUserQueryHandler.spec.ts   
    └── Shared
        ├── DependencyInjection.ts
        ├── application
        │   ├── ports
        │   │   ├── CryptoPort.ts
        │   │   ├── IdentityProvider.ts
        │   │   └── TimeService.ts
        │   └── queries
        │       └── Debug
        │           ├── LoadMatchEventsForMatchQuery.ts
        │           ├── LoadMatchEventsForMatchQueryHandler.ts
        │           ├── LoadMatchmakingEventsForGameQuery.ts
        │           └── LoadMatchmakingEventsForGameQueryHandler.ts
        ├── infrastructure
        │   ├── gateways
        │   │   └── Context
        │   │       ├── ConvexEventBus.ts
        │   │       └── FakeEventBus.ts
        │   ├── providers
        │   │   ├── IdentityProvider
        │   │   │   ├── FakeIdentityProvider.ts
        │   │   │   └── UuidIdentityProvider.ts
        │   │   └── Time
        │   │       ├── FakeTimeService.ts
        │   │       └── RealTimeService.ts
        │   └── seeds
        │       └── cards.jsonl
        └── specs
            └── helpers
                ├── fakes
                │   ├── GameDTO.ts
                │   ├── fakeCatalogCards.ts
                │   ├── fakeGameFilters.ts
                │   └── fakeGames.ts
                ├── requests
                │   ├── createFakeGame.ts
                │   └── getAllFrom.ts
                └── utils
                    └── TestingContainer.ts

```# Repository Instructions

## Important
- Run `pnpm install` before starting to work and wait for it to complete (be patient as it can take a while).
- If packages install is not working, stop the task immediately and inform the user.

## General Workflow
- Follow a **Test-Driven Development** approach.
- After each iteration or change, run `pnpm run lint` and `pnpm test`.
- Both commands must succeed before committing.
- Keep commits focused and describe the functional change.

## Testing Guidelines
- Tests use **Vitest** with jest-extended matchers.
- Use `pnpm test` to run them.
- Structure tests with the sections:
  ```ts
  // Arrange
  // Act
  // Assert
  ```
- Only perform **one** action in the `// Act` section.
- Use sociable tests that exercise use cases (command or query handlers).
- These act as acceptance tests; the domain is never tested directly.
- Avoid overly technical tests and solitary unit tests.
- Do **not** perform multiple state updates in one test; setup must be manual.
- `describe` blocks should use `describe("When ...")` to give context.
- Each `it` statement must start with `should` and stay close to the functional behaviour.
- Never leave `.only` or `.skip` in committed tests.

## Project Architecture
- The project follows **Clean Architecture + DDD + CQRS + TDD** organized by **Bounded Contexts**:
    - **Structure**: `src/client/[BoundedContext]` and `src/server/[BoundedContext]`
    - **Bounded Contexts**: `Authentication`, `DeckBuilding`, `Gaming`, `MatchMaking`, `Shared`
    - **Layers**: `domain`, `application`, `infrastructure`, `presentation`, `specs`
    - **ESLint boundary rules** enforce these constraints

## Key Patterns
- **CQRS**: Commands and Queries with dedicated handlers
- **Repository Pattern**: Interfaces in application, implementations in infrastructure
- **Presenter Pattern**: For view models and display logic
- **Domain Entities**: With factory methods, snapshots, and business logic
- **Value Objects**: Immutable objects representing domain concepts

## File Naming Conventions
- **Commands**: `[Action]Command.ts`, `[Action]CommandHandler.ts`
- **Queries**: `[Action]Query.ts`, `[Action]QueryHandler.ts`
- **Repositories**: `[Entity]Repository.ts` (interface), `Convex[Entity]Repository.ts`, `InMemory[Entity]Repository.ts`
- **Presenters**: `[Action]Presenter.ts` (interface), `[Action]WebPresenter.ts`
- **Tests**: `[action].spec.ts`
- **Domain**: `[Entity].ts`, `[ValueObject].ts`

## Import Conventions
- Path alias `@/*` points to the repository root
- Always use absolute imports with the `@/` alias
- Import from interfaces, not implementations
- Follow dependency direction: domain ← application ← infrastructure/presentation

## Tools
- **Linting**: `pnpm run lint` (TypeScript + ESLint with boundary rules)
- **Testing**: `pnpm test` (Vitest)
- **Coverage**: `pnpm run test:coverage`
- **Development**: `pnpm run dev:all` (Next.js + Convex)

## Frontend Specific
- **Framework**: Next.js 15 with React 19
- **UI**: Radix UI components and themes
- **State**: Zustand with clean architecture patterns
- **Internationalization**: Supports `en` and `fr` locales
- **Styling**: Tailwind CSS
- **Components**: Apply the humble object pattern. Move all logic outside of
  React components by relying on async thunks for use cases and selectors for
  querying data. Use hooks with no logic as the glue between components and
  use cases.

## Commit Requirements
- Keep the working directory clean before committing
- Include relevant tests for new features or fixes
- Ensure `pnpm run lint` and `pnpm test` pass before every commit
- Follow conventional commit messages describing functional changes
- Branch names should be descriptive and related to the task or feature in english


## Directory Overview
- **src** contains all bounded contexts organized under `client` and `server`:
    - `src/client/[BoundedContext]`
        - `application`
        - `domain`
        - `infrastructure`
        - `specs`
    - `src/server/[BoundedContext]`
        - `application`
        - `domain`
        - `infrastructure`
        - `presentation`
        - `specs`

Each bounded context repeats this layered structure. The complete directory tree
for the `src` folder is provided below as a reference so you can see the
contents of every `application`, `domain`, `infrastructure` and `presentation`
folder.