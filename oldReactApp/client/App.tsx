import "./global.css";

import { Toaster } from "@/components/ui/toaster";
import { createRoot } from "react-dom/client";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "@/contexts/AuthContext";
import Index from "./pages/Index";
import Login from "./pages/Login";
import Register from "./pages/Register";
import SessionHistory from "./pages/SessionHistory";
import SessionSetup from "./pages/SessionSetup";
import SessionSetupStep1 from "./pages/SessionSetupStep1";
import SessionSetupStep2 from "./pages/SessionSetupStep2";
import SessionSetupStep3 from "./pages/SessionSetupStep3";
import PlanningRoom from "./pages/PlanningRoom";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <AuthProvider>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Routes>
            <Route path="/" element={<Index />} />
            <Route path="/login" element={<Login />} />
            <Route path="/register" element={<Register />} />
            <Route path="/history" element={<SessionHistory />} />
            <Route path="/setup" element={<SessionSetup />} />
            <Route path="/setup/step1" element={<SessionSetupStep1 />} />
            <Route path="/setup/step2" element={<SessionSetupStep2 />} />
            <Route path="/setup/step3" element={<SessionSetupStep3 />} />
            <Route path="/room/:sessionId" element={<PlanningRoom />} />
            {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
      </TooltipProvider>
    </AuthProvider>
  </QueryClientProvider>
);

createRoot(document.getElementById("root")!).render(<App />);
