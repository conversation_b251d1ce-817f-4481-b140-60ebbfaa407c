import { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Copy, Check } from 'lucide-react';
import { useCopySessionCode } from '@/PokerPlanning/infrastructure/hooks/useCopySessionCode';
import { cn } from '@/lib/utils';

interface SessionCodeProps {
  code: string;
  className?: string;
}

export default function SessionCode({ code, className }: SessionCodeProps) {
  const copySessionCode = useCopySessionCode();
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    await copySessionCode(code);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <Badge
      variant="outline"
      className={cn('cursor-pointer', className)}
      onClick={handleCopy}
    >
      {copied ? <Check className="h-3 w-3 mr-1" /> : <Copy className="h-3 w-3 mr-1" />}
      {code}
    </Badge>
  );
}

