import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { 
  BarChart3, 
  Download, 
  CheckCircle, 
  Clock,
  Users,
  Target,
  FileText
} from "lucide-react";
import type { Story, EstimationType } from "./StoryManagement";

interface SessionRecapProps {
  stories: Story[];
  estimationType: EstimationType;
  sessionName: string;
  participants: Array<{
    id: string;
    name: string;
    initials: string;
  }>;
  onCloseSession: () => void;
}

export default function SessionRecap({
  stories,
  estimationType,
  sessionName,
  participants,
  onCloseSession
}: SessionRecapProps) {
  const completedStories = stories.filter(s => s.status === "completed");
  const totalEstimatedPoints = completedStories.reduce((sum, story) => {
    const estimate = story.finalEstimate;
    if (estimate && !isNaN(Number(estimate))) {
      return sum + Number(estimate);
    }
    return sum;
  }, 0);

  const getEstimateColor = (estimate: string) => {
    const card = estimationType.values.find(v => v.value === estimate);
    return card ? { bg: card.color, text: card.textColor } : { bg: "bg-gray-500", text: "text-white" };
  };

  const exportSummary = () => {
    const summary = {
      sessionName,
      estimationType: estimationType.name,
      participants: participants.map(p => p.name),
      stories: completedStories.map(story => ({
        title: story.title,
        description: story.description,
        finalEstimate: story.finalEstimate,
        votes: story.votes
      })),
      totalPoints: totalEstimatedPoints,
      exportedAt: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(summary, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${sessionName.replace(/\s+/g, '-').toLowerCase()}-summary.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="text-xs">
          <BarChart3 className="h-3 w-3 mr-1" />
          Session Recap
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-lg flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Session Summary
          </DialogTitle>
          <DialogDescription>
            Review all estimations from "{sessionName}"
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Session Overview */}
          <div className="grid grid-cols-2 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2 mb-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm font-medium">Completed Stories</span>
                </div>
                <div className="text-2xl font-bold">{completedStories.length}</div>
                <div className="text-xs text-muted-foreground">of {stories.length} total</div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2 mb-2">
                  <Target className="h-4 w-4 text-blue-600" />
                  <span className="text-sm font-medium">Total Points</span>
                </div>
                <div className="text-2xl font-bold">{totalEstimatedPoints || "N/A"}</div>
                <div className="text-xs text-muted-foreground">{estimationType.name} scale</div>
              </CardContent>
            </Card>
          </div>

          {/* Participants */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base flex items-center gap-2">
                <Users className="h-4 w-4" />
                Participants ({participants.length})
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="flex flex-wrap gap-2">
                {participants.map((participant) => (
                  <Badge key={participant.id} variant="secondary" className="text-xs">
                    {participant.name}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Story Results */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Story Estimates
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="space-y-3">
                {completedStories.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <Clock className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">No stories completed yet</p>
                  </div>
                ) : (
                  completedStories.map((story, index) => {
                    const colors = getEstimateColor(story.finalEstimate || "");
                    return (
                      <div key={story.id} className="border rounded-lg p-3">
                        <div className="flex items-start justify-between gap-3">
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-1">
                              <span className="text-xs font-medium text-muted-foreground">
                                Story #{index + 1}
                              </span>
                              <CheckCircle className="h-3 w-3 text-green-600" />
                            </div>
                            <h4 className="font-medium text-sm leading-tight mb-1">
                              {story.title}
                            </h4>
                            <p className="text-xs text-muted-foreground leading-relaxed">
                              {story.description}
                            </p>
                          </div>
                          <div className={`w-12 h-12 rounded-lg flex items-center justify-center font-bold text-sm ${colors.bg} ${colors.text} flex-shrink-0`}>
                            {story.finalEstimate}
                          </div>
                        </div>

                        {/* Individual Votes */}
                        {story.votes && Object.keys(story.votes).length > 0 && (
                          <div className="mt-3 pt-3 border-t">
                            <div className="text-xs font-medium text-muted-foreground mb-2">
                              Individual Votes:
                            </div>
                            <div className="flex flex-wrap gap-1">
                              {Object.entries(story.votes).map(([participantId, vote]) => {
                                const participant = participants.find(p => p.id === participantId);
                                const voteColors = getEstimateColor(vote);
                                return (
                                  <div key={participantId} className="flex items-center gap-1">
                                    <span className="text-xs text-muted-foreground">
                                      {participant?.name || 'Unknown'}:
                                    </span>
                                    <div className={`w-6 h-6 rounded text-xs font-bold flex items-center justify-center ${voteColors.bg} ${voteColors.text}`}>
                                      {vote}
                                    </div>
                                  </div>
                                );
                              })}
                            </div>
                          </div>
                        )}
                      </div>
                    );
                  })
                )}
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          <div className="flex gap-3">
            <Button 
              onClick={exportSummary} 
              variant="outline" 
              className="flex-1"
              size="sm"
            >
              <Download className="h-4 w-4 mr-2" />
              Export Summary
            </Button>
            <Button 
              onClick={onCloseSession} 
              className="flex-1"
              size="sm"
            >
              Close Session
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
