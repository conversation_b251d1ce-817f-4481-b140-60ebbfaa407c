import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  Plus, 
  Edit3, 
  Trash2, 
  Settings,
  CheckCircle,
  ArrowRight,
  BarChart3
} from "lucide-react";

export interface Story {
  id: string;
  title: string;
  description: string;
  status: "pending" | "active" | "completed";
  finalEstimate?: string;
  votes?: Record<string, string>;
}

export interface EstimationType {
  id: string;
  name: string;
  values: Array<{
    value: string;
    label: string;
    color: string;
    textColor: string;
    icon?: React.ReactNode;
  }>;
}

interface StoryManagementProps {
  stories: Story[];
  currentStoryIndex: number;
  estimationType: EstimationType;
  isHost: boolean;
  onAddStory: (story: Omit<Story, "id" | "status">) => void;
  onEditStory: (storyId: string, updates: Partial<Story>) => void;
  onDeleteStory: (storyId: string) => void;
  onNextStory: () => void;
  onCloseStory: (finalEstimate: string) => void;
  onChangeEstimationType: (type: EstimationType) => void;
}

const ESTIMATION_TYPES: EstimationType[] = [
  {
    id: "fibonacci",
    name: "Fibonacci",
    values: [
      { value: "0", label: "0", color: "bg-slate-500", textColor: "text-white" },
      { value: "0.5", label: "½", color: "bg-blue-500", textColor: "text-white" },
      { value: "1", label: "1", color: "bg-green-500", textColor: "text-white" },
      { value: "2", label: "2", color: "bg-green-600", textColor: "text-white" },
      { value: "3", label: "3", color: "bg-yellow-500", textColor: "text-slate-900" },
      { value: "5", label: "5", color: "bg-orange-500", textColor: "text-white" },
      { value: "8", label: "8", color: "bg-red-500", textColor: "text-white" },
      { value: "13", label: "13", color: "bg-purple-500", textColor: "text-white" },
      { value: "21", label: "21", color: "bg-pink-500", textColor: "text-white" },
      { value: "34", label: "34", color: "bg-indigo-500", textColor: "text-white" },
      { value: "?", label: "?", color: "bg-slate-600", textColor: "text-white" },
      { value: "☕", label: "☕", color: "bg-amber-600", textColor: "text-white" }
    ]
  },
  {
    id: "tshirt",
    name: "T-Shirt Sizes",
    values: [
      { value: "XS", label: "XS", color: "bg-green-500", textColor: "text-white" },
      { value: "S", label: "S", color: "bg-blue-500", textColor: "text-white" },
      { value: "M", label: "M", color: "bg-yellow-500", textColor: "text-slate-900" },
      { value: "L", label: "L", color: "bg-orange-500", textColor: "text-white" },
      { value: "XL", label: "XL", color: "bg-red-500", textColor: "text-white" },
      { value: "XXL", label: "XXL", color: "bg-purple-500", textColor: "text-white" },
      { value: "?", label: "?", color: "bg-slate-600", textColor: "text-white" },
      { value: "☕", label: "☕", color: "bg-amber-600", textColor: "text-white" }
    ]
  }
];

export default function StoryManagement({
  stories,
  currentStoryIndex,
  estimationType,
  isHost,
  onAddStory,
  onEditStory,
  onDeleteStory,
  onNextStory,
  onCloseStory,
  onChangeEstimationType
}: StoryManagementProps) {
  const [isAddingStory, setIsAddingStory] = useState(false);
  const [newStory, setNewStory] = useState({ title: "", description: "" });
  const [isEditingStory, setIsEditingStory] = useState(false);
  const [editingStory, setEditingStory] = useState({ title: "", description: "" });
  const [finalEstimate, setFinalEstimate] = useState("");

  const currentStory = stories[currentStoryIndex];
  useEffect(() => {
    setEditingStory({ title: currentStory.title, description: currentStory.description });
  }, [currentStory]);
  const hasNextStory = currentStoryIndex < stories.length - 1;
  const completedStories = stories.filter(s => s.status === "completed").length;

  const handleAddStory = () => {
    if (newStory.title.trim() && newStory.description.trim()) {
      onAddStory(newStory);
      setNewStory({ title: "", description: "" });
      setIsAddingStory(false);
    }
  };

  const handleCloseStory = () => {
    if (finalEstimate.trim()) {
      onCloseStory(finalEstimate);
      setFinalEstimate("");
    }
  };

  if (!isHost) {
    // Non-host view - just show current story status
    return (
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-base flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Session Progress
            </CardTitle>
            <Badge variant="outline" className="text-xs">
              {completedStories}/{stories.length} completed
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>Current Story:</span>
              <Badge variant="secondary">#{currentStoryIndex + 1}</Badge>
            </div>
            <div className="text-xs text-muted-foreground">
              Estimation Type: {estimationType.name}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Host Controls */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-base flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Host Controls
            </CardTitle>
            <Badge variant="outline" className="text-xs">
              {completedStories}/{stories.length} completed
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="pt-0 space-y-3">
          {/* Estimation Type Selection */}
          <div className="space-y-2">
            <Label className="text-xs font-medium">Estimation Type</Label>
            <Select 
              value={estimationType.id} 
              onValueChange={(value) => {
                const newType = ESTIMATION_TYPES.find(t => t.id === value);
                if (newType) onChangeEstimationType(newType);
              }}
            >
              <SelectTrigger className="h-9">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {ESTIMATION_TYPES.map((type) => (
                  <SelectItem key={type.id} value={type.id}>
                    {type.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Story Actions */}
          <div className="grid grid-cols-3 gap-2">
            <Dialog open={isAddingStory} onOpenChange={setIsAddingStory}>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm" className="text-xs">
                  <Plus className="h-3 w-3 mr-1" />
                  Add Story
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle className="text-base">Add New Story</DialogTitle>
                  <DialogDescription className="text-sm">
                    Create a new user story for estimation
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="title" className="text-sm">Title</Label>
                    <Input
                      id="title"
                      placeholder="Feature title"
                      value={newStory.title}
                      onChange={(e) => setNewStory(prev => ({ ...prev, title: e.target.value }))}
                      className="h-9"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="description" className="text-sm">Description</Label>
                    <Textarea
                      id="description"
                      placeholder="As a user, I want to..."
                      value={newStory.description}
                      onChange={(e) => setNewStory(prev => ({ ...prev, description: e.target.value }))}
                      className="min-h-20 text-sm"
                    />
                  </div>
                  <div className="flex gap-2">
                    <Button 
                      onClick={handleAddStory}
                      disabled={!newStory.title.trim() || !newStory.description.trim()}
                      className="flex-1"
                      size="sm"
                    >
                      Add Story
                    </Button>
                    <Button 
                      variant="outline" 
                      onClick={() => setIsAddingStory(false)}
                      size="sm"
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>

            <Dialog open={isEditingStory} onOpenChange={setIsEditingStory}>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm" className="text-xs">
                  <Edit3 className="h-3 w-3 mr-1" />
                  Edit Story
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle className="text-base">Edit Story</DialogTitle>
                  <DialogDescription className="text-sm">
                    Update the current story details
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="edit-title" className="text-sm">Title</Label>
                    <Input
                      id="edit-title"
                      value={editingStory.title}
                      onChange={(e) =>
                        setEditingStory((prev) => ({ ...prev, title: e.target.value }))
                      }
                      className="h-9"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="edit-description" className="text-sm">Description</Label>
                    <Textarea
                      id="edit-description"
                      value={editingStory.description}
                      onChange={(e) =>
                        setEditingStory((prev) => ({
                          ...prev,
                          description: e.target.value,
                        }))
                      }
                      className="min-h-20 text-sm"
                    />
                  </div>
                  <div className="flex gap-2">
                    <Button
                      onClick={() => {
                        onEditStory(currentStory.id, editingStory);
                        setIsEditingStory(false);
                      }}
                      disabled={!editingStory.title.trim() || !editingStory.description.trim()}
                      className="flex-1"
                      size="sm"
                    >
                      Save Changes
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => setIsEditingStory(false)}
                      size="sm"
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>

            <Dialog>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm" className="text-xs">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Close Story
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle className="text-base">Close Current Story</DialogTitle>
                  <DialogDescription className="text-sm">
                    Set the final estimate for "{currentStory?.title}"
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="estimate" className="text-sm">Final Estimate</Label>
                    <Select value={finalEstimate} onValueChange={setFinalEstimate}>
                      <SelectTrigger className="h-9">
                        <SelectValue placeholder="Choose final estimate" />
                      </SelectTrigger>
                      <SelectContent>
                        {estimationType.values.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      onClick={handleCloseStory}
                      disabled={!finalEstimate}
                      className="flex-1"
                      size="sm"
                    >
                      Close & {hasNextStory ? "Next Story" : "Finish Session"}
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>

          {/* Quick Actions */}
          {hasNextStory && (
            <Button 
              onClick={onNextStory}
              variant="secondary"
              size="sm"
              className="w-full text-xs"
            >
              <ArrowRight className="h-3 w-3 mr-1" />
              Skip to Next Story
            </Button>
          )}
        </CardContent>
      </Card>

      {/* Story Queue */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base">Story Queue</CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="space-y-2 max-h-32 overflow-y-auto">
            {stories.map((story, index) => (
              <div 
                key={story.id}
                className={`flex items-center gap-2 p-2 rounded border text-xs ${
                  index === currentStoryIndex 
                    ? 'border-primary bg-primary/5' 
                    : story.status === 'completed'
                    ? 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950/20'
                    : 'border-border'
                }`}
              >
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2">
                    <span className="font-medium">#{index + 1}</span>
                    {story.status === 'completed' && (
                      <CheckCircle className="h-3 w-3 text-green-600" />
                    )}
                    {index === currentStoryIndex && (
                      <Badge variant="secondary" className="text-xs px-1">Current</Badge>
                    )}
                  </div>
                  <p className="truncate text-muted-foreground">{story.title}</p>
                  {story.finalEstimate && (
                    <p className="text-xs text-green-600">Final: {story.finalEstimate}</p>
                  )}
                </div>
                {story.status === 'pending' && (
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    onClick={() => onDeleteStory(story.id)}
                    className="p-1 h-6 w-6 text-muted-foreground hover:text-destructive"
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export { ESTIMATION_TYPES };
