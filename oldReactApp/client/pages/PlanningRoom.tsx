import { useState, useEffect } from "react";
import { useParams, useSearchParams } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Users,
  Eye,
  EyeOff,
  RotateCcw,
  Check,
  ArrowLeft,
  Edit3,
  TrendingUp,
  TrendingDown,
  MessageCircle,
  <PERSON>tings,
  ArrowRight,
  CheckCircle,
  <PERSON>h,
  Bar<PERSON><PERSON>3,
} from "lucide-react";
import { Link } from "react-router-dom";
import StoryManagement, {
  ESTIMATION_TYPES,
  type Story,
  type EstimationType,
} from "@/components/StoryManagement";
import SessionCode from "@/components/SessionCode";
import SessionRecap from "@/components/SessionRecap";
import { useVoting } from "@/PokerPlanning/infrastructure/hooks/useVoting";
import { useAddStory } from "@/PokerPlanning/infrastructure/hooks/useAddStory";
import { useEditStory } from "@/PokerPlanning/infrastructure/hooks/useEditStory";
import { useDeleteStory } from "@/PokerPlanning/infrastructure/hooks/useDeleteStory";
import { useNextStory } from "@/PokerPlanning/infrastructure/hooks/useNextStory";
import { useChangeEstimationType } from '@/PokerPlanning/infrastructure/hooks/useChangeEstimationType';
import { useVotingStats } from '@/PokerPlanning/infrastructure/hooks/useVotingStats';
import { useEndSession } from '@/PokerPlanning/infrastructure/hooks/useEndSession';

// Mock data with actual votes for demonstration
const MOCK_PARTICIPANTS = [
  {
    id: "1",
    name: "Alex Chen",
    initials: "AC",
    voted: true,
    isHost: true,
    vote: "3",
  },
  {
    id: "2",
    name: "Sarah Kim",
    initials: "SK",
    voted: true,
    isHost: false,
    vote: "8",
  },
  {
    id: "3",
    name: "Mike Johnson",
    initials: "MJ",
    voted: false,
    isHost: false,
    vote: null,
  },
  {
    id: "4",
    name: "Emma Davis",
    initials: "ED",
    voted: true,
    isHost: false,
    vote: "5",
  },
];

const getInitialStories = (sessionId: string): Story[] => {
  try {
    const sessionData = localStorage.getItem(`session_${sessionId}`);
    if (sessionData) {
      const parsed = JSON.parse(sessionData);
      return parsed.stories.map((story: any, index: number) => ({
        ...story,
        status: index === 0 ? "active" : "pending",
        votes:
          index === 0
            ? {
                "1": "3",
                "2": "8",
                "4": "5",
              }
            : undefined,
      }));
    }
  } catch (error) {
    console.error("Error loading session data:", error);
  }

  // Fallback to default stories
  return [
    {
      id: "1",
      title: "User login with OAuth integration",
      description:
        "As a user, I want to log in using my Google/GitHub account so that I can access the application quickly without creating a new password.",
      status: "active",
      votes: {
        "1": "3",
        "2": "8",
        "4": "5",
      },
    },
    {
      id: "2",
      title: "Dashboard data visualization",
      description:
        "As a user, I want to see my project data in charts and graphs so that I can quickly understand the current status.",
      status: "pending",
    },
    {
      id: "3",
      title: "Real-time notifications",
      description:
        "As a user, I want to receive real-time notifications about important updates so that I stay informed.",
      status: "pending",
    },
  ];
};

export default function PlanningRoom() {
  const { sessionId } = useParams();
  const [searchParams] = useSearchParams();

  const [stories, setStories] = useState<Story[]>(() =>
    getInitialStories(sessionId || ""),
  );
  const [currentStoryIndex, setCurrentStoryIndex] = useState(0);
  const [estimationType, setEstimationType] = useState<EstimationType>(() => {
    try {
      const sessionData = localStorage.getItem(`session_${sessionId}`);
      if (sessionData) {
        const parsed = JSON.parse(sessionData);
        return parsed.estimationType || ESTIMATION_TYPES[0];
      }
    } catch (error) {
      console.error("Error loading estimation type:", error);
    }
    return ESTIMATION_TYPES[0];
  });
  const {
    selectCard,
    revealVotes,
    resetVotes,
    changeVote,
    selectedCard,
    votesRevealed,
    votingCardsVisible,
  } = useVoting();
  const changeEstimationType = useChangeEstimationType();
  const addStory = useAddStory();
  const editStory = useEditStory();
  const deleteStory = useDeleteStory();
  const nextStory = useNextStory();
  const [sessionCode, setSessionCode] = useState(sessionId || "ABC123");
  const [showEstimationTypeDialog, setShowEstimationTypeDialog] =
    useState(false);
  const [sessionName, setSessionName] = useState(() => {
    const paramSessionName = searchParams.get("sessionName");
    if (paramSessionName) return paramSessionName;

    try {
      const sessionData = localStorage.getItem(`session_${sessionId}`);
      if (sessionData) {
        const parsed = JSON.parse(sessionData);
        return parsed.sessionName || "Sprint 24 Planning";
      }
    } catch (error) {
      console.error("Error loading session name:", error);
    }
    return "Sprint 24 Planning";
  });

  // Get user info from URL params
  const userName = searchParams.get("name") || "Guest";
  const isHost =
    searchParams.get("host") === "true" || userName === "Alex Chen"; // Mock host check

  const currentStory = stories[currentStoryIndex];
  const votedCount = MOCK_PARTICIPANTS.filter((p) => p.voted).length;
  const totalCount = MOCK_PARTICIPANTS.length;
  const hasNextStory = currentStoryIndex < stories.length - 1;
  const completedStories = stories.filter(
    (s) => s.status === "completed",
  ).length;


  // Story Management Handlers
  const handleAddStory = (newStory: Omit<Story, "id" | "status">) => {
    const story = addStory(newStory.title, newStory.description);
    setStories((prev) => [...prev, story]);
  };

  const handleEditStory = (storyId: string, updates: Partial<Story>) => {
    const current = stories.find((s) => s.id === storyId);
    if (!current) return;
    const updated = editStory(storyId, {
      title: updates.title ?? current.title,
      description: updates.description ?? current.description,
    });
    setStories((prev) =>
      prev.map((story) => (story.id === storyId ? updated : story)),
    );
  };

  const handleDeleteStory = (storyId: string) => {
    deleteStory(storyId);
    setStories((prev) => prev.filter((story) => story.id !== storyId));
  };

  const handleCloseStory = (finalEstimate: string) => {
    setStories((prev) =>
      prev.map((story, index) =>
        index === currentStoryIndex
          ? {
              ...story,
              status: "completed",
              finalEstimate,
              votes: currentStory.votes,
            }
          : story,
      ),
    );

    if (currentStoryIndex < stories.length - 1) {
      setTimeout(() => {
        setCurrentStoryIndex((prev) => prev + 1);
        setStories((prev) =>
          prev.map((story, index) =>
            index === currentStoryIndex + 1
              ? { ...story, status: "active" }
              : story,
          ),
        );
          resetVotes();
      }, 500);
    }
  };

  const endSession = useEndSession();

  const handleChangeEstimationType = (newType: EstimationType) => {
    void changeEstimationType(sessionId || '', newType);
    setEstimationType(newType);
    setShowEstimationTypeDialog(false);
  };

  const stats = useVotingStats(MOCK_PARTICIPANTS);
  const highest = stats?.highest ?? [];
  const lowest = stats?.lowest ?? [];

  const getCardInfo = (value: string) => {
    return estimationType.values.find((card) => card.value === value);
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-muted/20">
      {/* Clean Header */}
      <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 sticky top-0 z-50">
        <div className="container mx-auto px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Link to="/">
                <Button variant="ghost" size="sm" className="p-2">
                  <ArrowLeft className="h-4 w-4" />
                </Button>
              </Link>
              <div>
                <h1 className="font-semibold text-foreground text-sm">
                  {sessionName}
                </h1>
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <span>
                    Story {currentStoryIndex + 1}/{stories.length}
                  </span>
                  <span>•</span>
                  <span>
                    {votedCount}/{totalCount} voted
                  </span>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <SessionCode code={sessionCode} className="text-xs hover:bg-muted" />

              {/* Close Session Button */}
              {isHost && (
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-xs text-destructive hover:text-destructive hover:bg-destructive/10"
                    >
                      <CheckCircle className="h-3 w-3 mr-1" />
                      End
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>End Session?</AlertDialogTitle>
                      <AlertDialogDescription>
                        This will close the session for all participants. You
                        can view the results summary before ending.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={() => void endSession()}
                        className="bg-destructive hover:bg-destructive/90"
                      >
                        End Session
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-4 space-y-4">
        {/* Current Story */}
        {currentStory && (
          <Card className="bg-card/50">
            <CardContent className="py-4">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Badge variant="outline" className="text-xs">
                    Story #{currentStoryIndex + 1}
                  </Badge>
                  {isHost ? (
                    <Dialog
                      open={showEstimationTypeDialog}
                      onOpenChange={setShowEstimationTypeDialog}
                    >
                      <DialogTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-xs text-muted-foreground hover:text-foreground h-auto p-1"
                        >
                          {estimationType.name}
                          <Settings className="h-3 w-3 ml-1" />
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-md">
                        <DialogHeader>
                          <DialogTitle className="text-base">
                            Change Estimation Type
                          </DialogTitle>
                          <DialogDescription className="text-sm">
                            This will reset all current votes and change the
                            estimation cards
                          </DialogDescription>
                        </DialogHeader>
                        <div className="space-y-4">
                          <div className="text-sm font-medium text-muted-foreground mb-3">
                            Current: {estimationType.name}
                          </div>

                          <div className="space-y-3">
                            {ESTIMATION_TYPES.map((type) => (
                              <div key={type.id} className="space-y-2">
                                <Button
                                  onClick={() =>
                                    handleChangeEstimationType(type)
                                  }
                                  variant={
                                    estimationType.id === type.id
                                      ? "default"
                                      : "outline"
                                  }
                                  className="w-full justify-start h-auto p-3"
                                  disabled={estimationType.id === type.id}
                                >
                                  <div className="text-left">
                                    <div className="font-medium">
                                      {type.name}
                                    </div>
                                    <div className="text-xs text-muted-foreground">
                                      {type.id === "fibonacci"
                                        ? "Story points with Fibonacci sequence"
                                        : "Relative sizing with T-shirt sizes"}
                                    </div>
                                  </div>
                                </Button>

                                {/* Preview cards */}
                                <div className="flex gap-1 px-3 pb-2">
                                  {type.values
                                    .slice(0, 6)
                                    .map((card, index) => (
                                      <div
                                        key={index}
                                        className={`w-6 h-6 rounded text-xs font-bold flex items-center justify-center ${card.color} ${card.textColor}`}
                                      >
                                        {card.icon || card.label}
                                      </div>
                                    ))}
                                  {type.values.length > 6 && (
                                    <div className="w-6 h-6 rounded bg-muted flex items-center justify-center text-xs">
                                      +{type.values.length - 6}
                                    </div>
                                  )}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      </DialogContent>
                    </Dialog>
                  ) : (
                    <div className="text-xs text-muted-foreground">
                      {estimationType.name}
                    </div>
                  )}
                </div>
                <div>
                  <h3 className="font-medium text-sm leading-tight mb-1">
                    {currentStory.title}
                  </h3>
                  <p className="text-xs text-muted-foreground leading-relaxed">
                    {currentStory.description}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Host Controls - Visible when host */}
        {isHost && votesRevealed && (
          <Card className="border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950/20">
            <CardContent className="py-4">
              <div className="space-y-3">
                <h4 className="font-medium text-sm text-blue-800 dark:text-blue-200 flex items-center gap-1">
                  <Settings className="h-3 w-3" />
                  Host Actions
                </h4>
                <div className="space-y-2">
                  <div className="grid grid-cols-2 gap-2">
                    {hasNextStory ? (
                      <Button
                        onClick={nextStory}
                        size="sm"
                        className="text-xs h-9"
                      >
                        <ArrowRight className="h-3 w-3 mr-1" />
                        Next Story
                      </Button>
                    ) : (
                      <Button
                        onClick={() => void endSession()}
                        size="sm"
                        className="text-xs h-9"
                      >
                        <CheckCircle className="h-3 w-3 mr-1" />
                        End Session
                      </Button>
                    )}

                    <Dialog>
                      <DialogTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-xs h-9"
                        >
                          <BarChart3 className="h-3 w-3 mr-1" />
                          Manage
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-md">
                        <DialogHeader>
                          <DialogTitle className="text-base">
                            Session Management
                          </DialogTitle>
                          <DialogDescription className="text-sm">
                            Manage stories and session settings
                          </DialogDescription>
                        </DialogHeader>
                        <div className="space-y-4">
                          <StoryManagement
                            stories={stories}
                            currentStoryIndex={currentStoryIndex}
                            estimationType={estimationType}
                            isHost={isHost}
                            onAddStory={handleAddStory}
                            onEditStory={handleEditStory}
                            onDeleteStory={handleDeleteStory}
                            onNextStory={nextStory}
                            onCloseStory={handleCloseStory}
                            onChangeEstimationType={handleChangeEstimationType}
                          />

                          <SessionRecap
                            stories={stories}
                            estimationType={estimationType}
                            sessionName={sessionName}
                            participants={MOCK_PARTICIPANTS}
                            onCloseSession={() => void endSession()}
                          />
                        </div>
                      </DialogContent>
                    </Dialog>
                  </div>

                  {/* Quick Settings */}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowEstimationTypeDialog(true)}
                    className="w-full text-xs h-8 text-blue-700 dark:text-blue-300"
                  >
                    <Settings className="h-3 w-3 mr-1" />
                    Change Estimation Type ({estimationType.name})
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Your Vote Status */}
        {selectedCard && !votingCardsVisible && (
          <Card className="border-primary/20 bg-primary/5">
            <CardContent className="py-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div
                    className={`w-10 h-10 rounded-lg flex items-center justify-center font-bold text-sm ${getCardInfo(selectedCard)?.color} ${getCardInfo(selectedCard)?.textColor}`}
                  >
                    {getCardInfo(selectedCard)?.icon ||
                      getCardInfo(selectedCard)?.label}
                  </div>
                  <div>
                    <p className="font-medium text-sm">
                      Your Vote: {selectedCard}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      Waiting for others...
                    </p>
                  </div>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={changeVote}
                  className="text-xs h-8"
                >
                  <Edit3 className="h-3 w-3 mr-1" />
                  Change
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Voting Cards */}
        {votingCardsVisible && !votesRevealed && (
          <Card>
            <CardContent className="py-4">
              <div className="space-y-3">
                <div className="text-center">
                  <h3 className="font-medium text-sm">Select Your Estimate</h3>
                  <p className="text-xs text-muted-foreground">
                    Tap a card to vote
                  </p>
                </div>
                <div className="grid grid-cols-4 gap-2">
                  {estimationType.values.map((card) => (
                    <button
                      key={card.value}
                      onClick={() => selectCard(card.value)}
                      className={`
                        aspect-[3/4] rounded-lg border-2 transition-all duration-200
                        flex items-center justify-center font-bold text-sm
                        active:scale-95 touch-manipulation shadow-sm
                        ${card.color} ${card.textColor}
                        border-transparent hover:scale-105 hover:shadow-md
                      `}
                    >
                      {card.icon || card.label}
                    </button>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Team Status - Improved */}
        {!votesRevealed ? (
          <Card>
            <CardContent className="py-3">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium text-sm">Team Status</h4>
                  <div className="flex items-center gap-3 text-xs text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span>Voted ({votedCount})</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-amber-500 rounded-full"></div>
                      <span>Waiting ({totalCount - votedCount})</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  {MOCK_PARTICIPANTS.map((participant) => (
                    <div
                      key={participant.id}
                      className={`flex items-center gap-3 p-3 rounded-lg border transition-all ${
                        participant.voted
                          ? "bg-green-50 border-green-200 dark:bg-green-950/20 dark:border-green-800"
                          : "bg-amber-50 border-amber-200 dark:bg-amber-950/20 dark:border-amber-800"
                      }`}
                    >
                      <Avatar className="h-8 w-8">
                        <AvatarFallback className="text-xs font-medium">
                          {participant.initials}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <p className="text-sm font-medium">
                            {participant.name}
                          </p>
                          {participant.isHost && (
                            <Badge
                              variant="secondary"
                              className="text-xs px-1.5 py-0"
                            >
                              Host
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center gap-1 mt-0.5">
                          {participant.voted ? (
                            <div className="flex items-center gap-1">
                              <Check className="h-3 w-3 text-green-600" />
                              <span className="text-xs text-green-700 dark:text-green-400 font-medium">
                                Voted
                              </span>
                            </div>
                          ) : (
                            <div className="flex items-center gap-1">
                              <div className="w-3 h-3 border-2 border-amber-500 rounded-full animate-pulse"></div>
                              <span className="text-xs text-amber-700 dark:text-amber-400 font-medium">
                                Voting...
                              </span>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Vote indicator */}
                      {participant.voted ? (
                        <div className="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
                          <Check className="h-5 w-5 text-white" />
                        </div>
                      ) : (
                        <div className="w-10 h-10 bg-amber-200 border-2 border-amber-400 rounded-lg flex items-center justify-center">
                          <div className="w-3 h-3 bg-amber-500 rounded-full animate-pulse"></div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        ) : (
          /* Results View - Prominent Display */
          <div className="space-y-4">
            {/* Main Results - Hero Section */}
            {stats && (
              <Card className="border-primary/20 bg-primary/5">
                <CardHeader className="pb-3">
                  <CardTitle className="text-center text-lg">
                    Estimation Results
                  </CardTitle>
                </CardHeader>
                <CardContent className="py-4">
                  {/* Primary Result - Average */}
                  <div className="text-center mb-6">
                    <div className="text-4xl font-bold text-primary mb-2">
                      {stats.avg}
                    </div>
                    <div className="text-sm text-muted-foreground font-medium">
                      Team Average
                    </div>
                  </div>

                  {/* Range Display */}
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div className="text-center p-4 bg-background rounded-lg border">
                      <div className="text-2xl font-bold text-red-600 mb-1">
                        {stats.min}
                      </div>
                      <div className="text-xs text-muted-foreground font-medium">
                        Lowest
                      </div>
                    </div>
                    <div className="text-center p-4 bg-background rounded-lg border">
                      <div className="text-2xl font-bold text-green-600 mb-1">
                        {stats.max}
                      </div>
                      <div className="text-xs text-muted-foreground font-medium">
                        Highest
                      </div>
                    </div>
                  </div>

                  {/* Consensus Indicator */}
                  <div className="text-center">
                    {stats.min === stats.max ? (
                      <Badge className="bg-green-500 text-white">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Perfect Consensus!
                      </Badge>
                    ) : stats.max - stats.min <= 3 ? (
                      <Badge variant="secondary">Good Alignment</Badge>
                    ) : (
                      <Badge
                        variant="outline"
                        className="border-amber-500 text-amber-700"
                      >
                        <MessageCircle className="h-3 w-3 mr-1" />
                        Discussion Needed
                      </Badge>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Individual Votes - Discussion Focus */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-base flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  Individual Votes
                </CardTitle>
                {(highest.length > 0 || lowest.length > 0) && (
                  <CardDescription className="text-sm text-amber-600 dark:text-amber-400">
                    <MessageCircle className="h-3 w-3 mr-1 inline" />
                    Extreme voters should explain their reasoning
                  </CardDescription>
                )}
              </CardHeader>
              <CardContent className="pt-0">
                <div className="space-y-3">
                  {MOCK_PARTICIPANTS.filter((p) => p.voted).map(
                    (participant) => {
                      const isHighest = highest.some(
                        (h) => h.id === participant.id,
                      );
                      const isLowest = lowest.some(
                        (l) => l.id === participant.id,
                      );
                      const needsDiscussion = isHighest || isLowest;
                      const cardInfo = getCardInfo(participant.vote!);

                      return (
                        <div
                          key={participant.id}
                          className={`flex items-center gap-3 p-3 rounded-lg border transition-all ${
                            needsDiscussion
                              ? "bg-amber-50 border-amber-300 shadow-sm dark:bg-amber-950/30 dark:border-amber-700"
                              : "bg-muted/30 border-border"
                          }`}
                        >
                          <Avatar className="h-8 w-8">
                            <AvatarFallback className="text-xs font-medium">
                              {participant.initials}
                            </AvatarFallback>
                          </Avatar>
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <p className="text-sm font-medium">
                                {participant.name}
                              </p>
                              {participant.isHost && (
                                <Badge
                                  variant="secondary"
                                  className="text-xs px-1.5 py-0"
                                >
                                  Host
                                </Badge>
                              )}
                              {isHighest && (
                                <Badge
                                  variant="outline"
                                  className="text-xs px-1.5 py-0 border-green-500 text-green-700"
                                >
                                  <TrendingUp className="h-3 w-3 mr-1" />
                                  Highest
                                </Badge>
                              )}
                              {isLowest && (
                                <Badge
                                  variant="outline"
                                  className="text-xs px-1.5 py-0 border-red-500 text-red-700"
                                >
                                  <TrendingDown className="h-3 w-3 mr-1" />
                                  Lowest
                                </Badge>
                              )}
                            </div>
                            {needsDiscussion && (
                              <p className="text-xs text-amber-700 dark:text-amber-400 font-medium">
                                💬 Should explain their estimate
                              </p>
                            )}
                          </div>
                          <div
                            className={`w-12 h-12 rounded-lg flex items-center justify-center font-bold text-lg shadow-sm ${cardInfo?.color} ${cardInfo?.textColor}`}
                          >
                            {cardInfo?.icon || cardInfo?.label}
                          </div>
                        </div>
                      );
                    },
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Action Buttons - Fixed Bottom with Safe Area */}
        <div className="fixed bottom-0 left-0 right-0 bg-background/95 backdrop-blur border-t p-4 pb-[calc(1rem+env(safe-area-inset-bottom))]">
          <div className="container mx-auto max-w-md space-y-2">
            <Button
              onClick={revealVotes}
              disabled={votedCount === 0}
              className="w-full h-12 text-base font-medium"
              size="lg"
            >
              {votesRevealed ? (
                <EyeOff className="h-4 w-4 mr-2" />
              ) : (
                <Eye className="h-4 w-4 mr-2" />
              )}
              {votesRevealed ? "Hide Votes" : "Reveal Votes"}
            </Button>

            {votesRevealed && (
              <Button
                onClick={resetVotes}
                variant="outline"
                className="w-full h-11 text-sm"
              >
                <RotateCcw className="h-4 w-4 mr-2" />
                New Round
              </Button>
            )}
          </div>
        </div>

        {/* Spacer for fixed bottom buttons */}
        <div className="h-[calc(6rem+env(safe-area-inset-bottom))]"></div>
      </div>
    </div>
  );
}
