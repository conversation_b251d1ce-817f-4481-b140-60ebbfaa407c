import { useState, useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  ArrowLeft,
  ArrowRight,
  Plus,
  Trash2,
  FileText,
  ChevronRight,
  Edit3,
  Check,
  X,
} from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Link } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { useAddSetupStory } from "@/PokerPlanning/infrastructure/hooks/useAddSetupStory";
import { useEditSetupStory } from "@/PokerPlanning/infrastructure/hooks/useEditSetupStory";
import { useDeleteSetupStory } from "@/PokerPlanning/infrastructure/hooks/useDeleteSetupStory";
import { useSetupStories } from "@/PokerPlanning/infrastructure/hooks/useSetupStories";
import { storyStore } from "@/PokerPlanning/infrastructure/stores/storyStore";

interface Story {
  id: string;
  title: string;
  description: string;
}

export default function SessionSetupStep2() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { user } = useAuth();

  const stories = useSetupStories();
  const addStory = useAddSetupStory();
  const editStoryCmd = useEditSetupStory();
  const deleteStory = useDeleteSetupStory();
  const [newStory, setNewStory] = useState({ title: "", description: "" });
  const [sessionName, setSessionName] = useState("");
  const [editingStory, setEditingStory] = useState<string | null>(null);
  const [editStory, setEditStory] = useState({ title: "", description: "" });

  // Load data from previous step
  useEffect(() => {
    const savedSetup = localStorage.getItem("session_setup");
    if (savedSetup) {
      try {
        const setupData = JSON.parse(savedSetup);
        setSessionName(setupData.sessionName || "");
        if (setupData.stories) {
          storyStore.setState({ stories: setupData.stories, currentStoryIndex: 0 });
        }
      } catch (error) {
        console.error("Error loading setup data:", error);
      }
    }

    const paramSessionName = searchParams.get("sessionName");
    if (paramSessionName) {
      setSessionName(paramSessionName);
    }
  }, [searchParams]);

  const handleAddStory = () => {
    if (newStory.title.trim()) {
      void addStory(newStory.title.trim(), newStory.description.trim() || "");
      setNewStory({ title: "", description: "" });
    }
  };

  const handleDeleteStory = (storyId: string) => {
    void deleteStory(storyId);
  };

  const handleEditStory = (storyId: string) => {
    const story = stories.find((s) => s.id === storyId);
    if (story) {
      setEditingStory(storyId);
      setEditStory({ title: story.title, description: story.description });
    }
  };

  const handleSaveEdit = () => {
    if (editingStory && editStory.title.trim()) {
      void editStoryCmd(
        editingStory,
        editStory.title.trim(),
        editStory.description.trim(),
      );
      setEditingStory(null);
      setEditStory({ title: "", description: "" });
    }
  };

  const handleCancelEdit = () => {
    setEditingStory(null);
    setEditStory({ title: "", description: "" });
  };

  const handleBack = () => {
    navigate("/setup/step1");
  };

  const handleNext = () => {
    if (stories.length > 0) {
      navigate("/setup/step3");
    }
  };

  const canContinue = stories.length > 0;

  if (!user) {
    navigate(`/login?redirect=${encodeURIComponent("/setup/step2")}`);
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-muted/20">
      {/* Header */}
      <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="sm"
              className="p-2"
              onClick={handleBack}
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <h1 className="text-xl font-bold text-foreground">
                Session Setup
              </h1>
              <p className="text-xs text-muted-foreground">
                Step 2 of 3 • Add Stories
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-6 max-w-lg">
        <div className="space-y-6">
          {/* Progress */}
          <Card>
            <CardContent className="py-4">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="font-medium">Setup Progress</span>
                  <span className="text-muted-foreground">2 of 3</span>
                </div>
                <Progress value={66} className="h-2" />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span className="text-green-600">✓ Configuration</span>
                  <span className="font-medium text-primary">Stories</span>
                  <span>Review</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Session Info */}
          <Card className="border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950/20">
            <CardContent className="py-4">
              <div className="text-center">
                <h2 className="font-semibold text-base mb-1 text-blue-800 dark:text-blue-200">
                  {sessionName}
                </h2>
                <p className="text-sm text-blue-600 dark:text-blue-300">
                  Add the user stories you want to estimate
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Add New Story */}
          <Card>
            <CardHeader className="pb-4">
              <CardTitle className="text-lg flex items-center gap-2">
                <Plus className="h-5 w-5" />
                Add Story
              </CardTitle>
              <CardDescription>
                Create user stories for your team to estimate
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="storyTitle" className="text-sm font-medium">
                  Story Title
                </Label>
                <Input
                  id="storyTitle"
                  type="text"
                  placeholder="User login with OAuth integration"
                  value={newStory.title}
                  onChange={(e) =>
                    setNewStory((prev) => ({ ...prev, title: e.target.value }))
                  }
                  className="h-11 text-sm"
                />
              </div>

              <div className="space-y-2">
                <Label
                  htmlFor="storyDescription"
                  className="text-sm font-medium"
                >
                  Description{" "}
                  <span className="text-muted-foreground font-normal">
                    (optional)
                  </span>
                </Label>
                <Textarea
                  id="storyDescription"
                  placeholder="As a user, I want to... (optional details)"
                  value={newStory.description}
                  onChange={(e) =>
                    setNewStory((prev) => ({
                      ...prev,
                      description: e.target.value,
                    }))
                  }
                  className="min-h-20 text-sm resize-none"
                />
              </div>

              <Button
                onClick={handleAddStory}
                disabled={!newStory.title.trim()}
                className="w-full h-11"
                size="sm"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Story
              </Button>
            </CardContent>
          </Card>

          {/* Stories List */}
          <Card>
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Stories Added
                </CardTitle>
                <Badge variant="outline" className="text-sm">
                  {stories.length} {stories.length === 1 ? "story" : "stories"}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              {stories.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <FileText className="h-12 w-12 mx-auto mb-3 opacity-50" />
                  <p className="text-base font-medium mb-1">No stories yet</p>
                  <p className="text-sm">Add at least one story to continue</p>
                </div>
              ) : (
                <div className="space-y-3 max-h-64 overflow-y-auto">
                  {stories.map((story, index) => (
                    <div key={story.id} className="border rounded-lg p-3">
                      <div className="flex items-start justify-between gap-3">
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-2">
                            <Badge variant="secondary" className="text-xs">
                              Story #{index + 1}
                            </Badge>
                          </div>
                          <h4 className="font-medium text-sm leading-tight mb-1">
                            {story.title}
                          </h4>
                          {story.description && (
                            <p className="text-xs text-muted-foreground leading-relaxed">
                              {story.description}
                            </p>
                          )}
                        </div>
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="p-1 h-8 w-8 text-muted-foreground hover:text-destructive hover:bg-destructive/10 flex-shrink-0"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Delete Story?</AlertDialogTitle>
                              <AlertDialogDescription>
                                Are you sure you want to delete "{story.title}"?
                                This action cannot be undone.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancel</AlertDialogCancel>
                              <AlertDialogAction
                                onClick={() => handleDeleteStory(story.id)}
                                className="bg-destructive hover:bg-destructive/90"
                              >
                                Delete Story
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Navigation */}
          <div className="flex gap-3">
            <Button
              onClick={handleBack}
              variant="outline"
              className="flex-1 h-12"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>

            <Button
              onClick={handleNext}
              disabled={!canContinue}
              className="flex-1 h-12"
            >
              {canContinue ? (
                <>
                  Review & Start
                  <ArrowRight className="h-4 w-4 ml-2" />
                </>
              ) : (
                <>
                  Add Stories First
                  <ChevronRight className="h-4 w-4 ml-2" />
                </>
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
