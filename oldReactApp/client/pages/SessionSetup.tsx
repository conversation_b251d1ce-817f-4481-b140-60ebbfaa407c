import { useState } from "react";
import { useSearchParams } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  Plus, 
  Trash2, 
  ArrowLeft, 
  Play,
  Settings,
  FileText,
  Zap
} from "lucide-react";
import { Link } from "react-router-dom";
import {
  ESTIMATION_TYPES,
  type EstimationType as UiEstimationType,
} from "@/components/StoryManagement";
import {
  type EstimationType,
  type Story,
} from "@/PokerPlanning/application/commands/startSession/startSessionCommand";
import { useStartSession } from "@/PokerPlanning/infrastructure/hooks/useStartSession";

export default function SessionSetup() {
  const [searchParams] = useSearchParams();

  const [sessionName, setSessionName] = useState(
    searchParams.get("sessionName") || "",
  );
  const [estimationType, setEstimationType] =
    useState<UiEstimationType>(ESTIMATION_TYPES[0]);
  const [stories, setStories] = useState<Story[]>([]);
  const [newStory, setNewStory] = useState({ title: "", description: "" });
  const startSession = useStartSession();

  const handleAddStory = () => {
    if (newStory.title.trim() && newStory.description.trim()) {
      const story: Story = {
        id: Date.now().toString(),
        title: newStory.title.trim(),
        description: newStory.description.trim()
      };
      setStories(prev => [...prev, story]);
      setNewStory({ title: "", description: "" });
    }
  };

  const handleDeleteStory = (storyId: string) => {
    setStories(prev => prev.filter(story => story.id !== storyId));
  };

  const handleStartSession = () => {
    if (sessionName.trim() && stories.length > 0) {
      const adaptedEstimationType: EstimationType = {
        id: estimationType.id,
        name: estimationType.name,
        values: estimationType.values.map((v) => ({
          value: v.value,
          label: v.label,
          color: v.color,
          textColor: v.textColor,
        })),
      };
      const adaptedStories: Story[] = stories.map((s) => ({
        id: s.id,
        title: s.title,
        description: s.description,
      }));
      void startSession(sessionName, adaptedEstimationType, adaptedStories);
    }
  };

  const canStart = sessionName.trim() && stories.length > 0;

  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-muted/20">
      {/* Header */}
      <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center gap-3">
            <Link to="/">
              <Button variant="ghost" size="sm" className="p-2">
                <ArrowLeft className="h-4 w-4" />
              </Button>
            </Link>
            <div>
              <h1 className="text-xl font-bold text-foreground">Session Setup</h1>
              <p className="text-xs text-muted-foreground">Configure your planning session</p>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-6 space-y-6">
        {/* Session Configuration */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Session Configuration
            </CardTitle>
            <CardDescription className="text-sm">
              Set up your planning session details
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-0 space-y-4">
            {/* Session Name */}
            <div className="space-y-2">
              <Label htmlFor="sessionName" className="text-sm font-medium">Session Name</Label>
              <Input
                id="sessionName"
                type="text"
                placeholder="Sprint 24 Planning"
                value={sessionName}
                onChange={(e) => setSessionName(e.target.value)}
                className="h-10 text-sm"
              />
            </div>

            {/* Estimation Type */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Estimation Type</Label>
              <Select 
                value={estimationType.id} 
                onValueChange={(value) => {
                  const newType = ESTIMATION_TYPES.find(t => t.id === value);
                  if (newType) setEstimationType(newType);
                }}
              >
                <SelectTrigger className="h-10">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {ESTIMATION_TYPES.map((type) => (
                    <SelectItem key={type.id} value={type.id}>
                      <div className="flex items-center gap-2">
                        <span>{type.name}</span>
                        <div className="flex gap-1">
                          {type.values.slice(0, 4).map((card, index) => (
                            <div 
                              key={index}
                              className={`w-4 h-4 rounded text-xs flex items-center justify-center ${card.color} ${card.textColor}`}
                            >
                              {card.icon ? "?" : card.label}
                            </div>
                          ))}
                          {type.values.length > 4 && (
                            <span className="text-xs text-muted-foreground">...</span>
                          )}
                        </div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground">
                Choose how your team will estimate story complexity
              </p>
            </div>

            {/* Estimation Preview */}
            <div className="p-3 bg-muted/30 rounded-lg">
              <p className="text-xs font-medium text-muted-foreground mb-2">Preview: {estimationType.name}</p>
              <div className="flex flex-wrap gap-1">
                {estimationType.values.map((card, index) => (
                  <div 
                    key={index}
                    className={`w-8 h-8 rounded text-xs font-bold flex items-center justify-center ${card.color} ${card.textColor}`}
                  >
                    {card.icon || card.label}
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Add New Story */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Add Story
            </CardTitle>
            <CardDescription className="text-sm">
              Add user stories for your team to estimate
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-0 space-y-4">
            <div className="space-y-2">
              <Label htmlFor="storyTitle" className="text-sm font-medium">Story Title</Label>
              <Input
                id="storyTitle"
                type="text"
                placeholder="User login with OAuth integration"
                value={newStory.title}
                onChange={(e) => setNewStory(prev => ({ ...prev, title: e.target.value }))}
                className="h-10 text-sm"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="storyDescription" className="text-sm font-medium">Description</Label>
              <Textarea
                id="storyDescription"
                placeholder="As a user, I want to log in using my Google/GitHub account so that I can access the application quickly without creating a new password."
                value={newStory.description}
                onChange={(e) => setNewStory(prev => ({ ...prev, description: e.target.value }))}
                className="min-h-20 text-sm resize-none"
              />
            </div>
            
            <Button 
              onClick={handleAddStory}
              disabled={!newStory.title.trim() || !newStory.description.trim()}
              className="w-full"
              size="sm"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Story
            </Button>
          </CardContent>
        </Card>

        {/* Stories List */}
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-base flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Stories to Estimate
              </CardTitle>
              <Badge variant="outline" className="text-xs">
                {stories.length} {stories.length === 1 ? 'story' : 'stories'}
              </Badge>
            </div>
            <CardDescription className="text-sm">
              Review and manage the stories for estimation
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-0">
            {stories.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <FileText className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">No stories added yet</p>
                <p className="text-xs">Add at least one story to start your session</p>
              </div>
            ) : (
              <div className="space-y-3 max-h-64 overflow-y-auto">
                {stories.map((story, index) => (
                  <div key={story.id} className="border rounded-lg p-3">
                    <div className="flex items-start justify-between gap-3">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <Badge variant="secondary" className="text-xs">
                            Story #{index + 1}
                          </Badge>
                        </div>
                        <h4 className="font-medium text-sm leading-tight mb-1">
                          {story.title}
                        </h4>
                        <p className="text-xs text-muted-foreground leading-relaxed">
                          {story.description}
                        </p>
                      </div>
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        onClick={() => handleDeleteStory(story.id)}
                        className="p-1 h-6 w-6 text-muted-foreground hover:text-destructive flex-shrink-0"
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Start Session */}
        <Card className={canStart ? "border-primary/20 bg-primary/5" : ""}>
          <CardContent className="py-6">
            <div className="text-center space-y-4">
              <div>
                <h3 className="font-medium text-base mb-1">Ready to Start?</h3>
                <p className="text-sm text-muted-foreground">
                  {canStart 
                    ? `Start your ${estimationType.name} planning session with ${stories.length} ${stories.length === 1 ? 'story' : 'stories'}`
                    : `Add a session name and at least one story to continue`
                  }
                </p>
              </div>
              
              <Button 
                onClick={handleStartSession}
                disabled={!canStart}
                className="w-full h-12 text-base font-medium"
                size="lg"
              >
                {canStart ? (
                  <>
                    <Play className="h-4 w-4 mr-2" />
                    Start Planning Session
                  </>
                ) : (
                  <>
                    <Zap className="h-4 w-4 mr-2" />
                    Complete Setup First
                  </>
                )}
              </Button>
              
              {canStart && (
                <p className="text-xs text-muted-foreground">
                  You'll be taken to the planning room as the host
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
