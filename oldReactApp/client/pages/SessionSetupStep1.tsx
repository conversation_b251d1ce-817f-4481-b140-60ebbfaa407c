import { useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Progress } from "@/components/ui/progress";
import {
  ArrowLeft,
  ArrowRight,
  Settings,
  ChevronRight,
  X
} from "lucide-react";
import { Link } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { ESTIMATION_TYPES, type EstimationType } from "@/components/StoryManagement";
import { useConfigureSession } from "@/PokerPlanning/infrastructure/hooks/useConfigureSession";

export default function SessionSetupStep1() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { user } = useAuth();
  const configureSession = useConfigureSession();

  const [sessionName, setSessionName] = useState(searchParams.get("sessionName") || "");
  const [baseEstimationType, setBaseEstimationType] = useState(ESTIMATION_TYPES[0]);
  const [enabledValues, setEnabledValues] = useState<string[]>(() =>
    ESTIMATION_TYPES[0].values.map(v => v.value)
  );

  // Create custom estimation type based on enabled values
  const estimationType: EstimationType = {
    ...baseEstimationType,
    values: baseEstimationType.values.filter(v => enabledValues.includes(v.value))
  };

  const handleEstimationTypeChange = (newTypeId: string) => {
    const newType = ESTIMATION_TYPES.find(t => t.id === newTypeId);
    if (newType) {
      setBaseEstimationType(newType);
      // Reset enabled values to all values for new type
      setEnabledValues(newType.values.map(v => v.value));
    }
  };

  const handleValueToggle = (value: string) => {
    setEnabledValues(prev => {
      const newValues = prev.includes(value)
        ? prev.filter(v => v !== value)
        : [...prev, value];

      // Ensure at least 3 values are enabled
      return newValues.length >= 3 ? newValues : prev;
    });
  };

  const handleNext = () => {
    if (sessionName.trim() && estimationType.values.length >= 3) {
      void configureSession(sessionName.trim(), estimationType);
    }
  };

  const canContinue = sessionName.trim() && estimationType.values.length >= 3;

  if (!user) {
    navigate(`/login?redirect=${encodeURIComponent('/setup/step1')}`);
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-muted/20">
      {/* Header */}
      <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center gap-3">
            <Link to="/">
              <Button variant="ghost" size="sm" className="p-2">
                <ArrowLeft className="h-4 w-4" />
              </Button>
            </Link>
            <div>
              <h1 className="text-xl font-bold text-foreground">Session Setup</h1>
              <p className="text-xs text-muted-foreground">Step 1 of 3 • Basic Configuration</p>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-6 max-w-lg">
        <div className="space-y-6">
          {/* Progress */}
          <Card>
            <CardContent className="py-4">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="font-medium">Setup Progress</span>
                  <span className="text-muted-foreground">1 of 3</span>
                </div>
                <Progress value={33} className="h-2" />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span className="font-medium text-primary">Configuration</span>
                  <span>Stories</span>
                  <span>Review</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Welcome Message */}
          <Card className="border-primary/20 bg-primary/5">
            <CardContent className="py-4">
              <div className="text-center">
                <h2 className="font-semibold text-base mb-1">Welcome, {user.name}!</h2>
                <p className="text-sm text-muted-foreground">
                  Let's set up your planning session step by step
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Session Configuration */}
          <Card>
            <CardHeader className="pb-4">
              <CardTitle className="text-lg flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Basic Configuration
              </CardTitle>
              <CardDescription>
                Set up the fundamental details for your planning session
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Session Name */}
              <div className="space-y-3">
                <Label htmlFor="sessionName" className="text-base font-medium">Session Name</Label>
                <Input
                  id="sessionName"
                  type="text"
                  placeholder="Sprint 24 Planning"
                  value={sessionName}
                  onChange={(e) => setSessionName(e.target.value)}
                  className="h-12 text-base"
                />
                <p className="text-sm text-muted-foreground">
                  Give your session a descriptive name that your team will recognize
                </p>
              </div>

              {/* Estimation Type */}
              <div className="space-y-3">
                <Label className="text-base font-medium">Estimation Method</Label>
                <Select
                  value={baseEstimationType.id}
                  onValueChange={handleEstimationTypeChange}
                >
                  <SelectTrigger className="h-12">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {ESTIMATION_TYPES.map((type) => (
                      <SelectItem key={type.id} value={type.id}>
                        <div className="flex items-center gap-3 py-1">
                          <div>
                            <div className="font-medium">{type.name}</div>
                            <div className="text-xs text-muted-foreground">
                              {type.id === 'fibonacci' 
                                ? 'Story points with Fibonacci sequence'
                                : 'Relative sizing with T-shirt sizes'
                              }
                            </div>
                          </div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <p className="text-sm text-muted-foreground">
                  Choose how your team will estimate the complexity of stories
                </p>
              </div>

              {/* Customize Values */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label className="text-sm font-medium">Customize Values</Label>
                  <Badge variant="outline" className="text-xs">
                    {estimationType.values.length} selected
                  </Badge>
                </div>

                <div className="p-3 border rounded-lg space-y-3">
                  <p className="text-xs text-muted-foreground">
                    Tap values to include/exclude them. Minimum 3 values required.
                  </p>

                  <div className="grid grid-cols-4 gap-2">
                    {baseEstimationType.values.map((card, index) => {
                      const isEnabled = enabledValues.includes(card.value);
                      const canDisable = enabledValues.length > 3;

                      return (
                        <button
                          key={index}
                          onClick={() => handleValueToggle(card.value)}
                          disabled={isEnabled && !canDisable}
                          className={`
                            relative aspect-square rounded text-xs font-bold flex items-center justify-center
                            transition-all duration-200 border-2
                            ${isEnabled
                              ? `${card.color} ${card.textColor} border-transparent`
                              : 'bg-muted/50 text-muted-foreground border-dashed border-muted-foreground/30'
                            }
                            ${isEnabled && !canDisable ? 'cursor-not-allowed' : 'cursor-pointer hover:scale-105'}
                          `}
                          title={`${card.label}${card.icon ? ' (Special card)' : ''} - ${isEnabled ? 'Click to remove' : 'Click to add'}`}
                        >
                          {card.icon || card.label}
                          {!isEnabled && (
                            <div className="absolute inset-0 flex items-center justify-center">
                              <X className="h-3 w-3" />
                            </div>
                          )}
                        </button>
                      );
                    })}
                  </div>
                </div>
              </div>

              {/* Selected Values Preview */}
              <div className="p-3 bg-primary/5 rounded-lg">
                <p className="text-sm font-medium text-primary mb-2">
                  Selected Cards ({estimationType.values.length})
                </p>
                <div className="flex gap-2 overflow-x-auto pb-1">
                  {estimationType.values.map((card, index) => (
                    <div
                      key={index}
                      className={`flex-shrink-0 w-8 h-8 rounded text-xs font-bold flex items-center justify-center ${card.color} ${card.textColor}`}
                      title={card.label}
                    >
                      {card.icon || card.label}
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Next Button */}
          <Card className={canContinue ? "border-primary/20 bg-primary/5" : ""}>
            <CardContent className="py-6">
              <div className="text-center space-y-4">
                <div>
                  <h3 className="font-medium text-base mb-1">Ready for Next Step?</h3>
                  <p className="text-sm text-muted-foreground">
                    {canContinue
                      ? "Let's add the stories you want to estimate"
                      : !sessionName.trim()
                        ? "Please enter a session name to continue"
                        : "Please select at least 3 estimation values"
                    }
                  </p>
                </div>
                
                <Button 
                  onClick={handleNext}
                  disabled={!canContinue}
                  className="w-full h-12 text-base font-medium"
                  size="lg"
                >
                  {canContinue ? (
                    <>
                      Next: Add Stories
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </>
                  ) : !sessionName.trim() ? (
                    <>
                      Enter Session Name
                      <ChevronRight className="h-4 w-4 ml-2" />
                    </>
                  ) : (
                    <>
                      Select More Values
                      <ChevronRight className="h-4 w-4 ml-2" />
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
