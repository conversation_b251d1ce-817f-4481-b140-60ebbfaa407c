import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  ArrowLeft,
  Play,
  Settings,
  FileText,
  Users,
  CheckCircle
} from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import type { EstimationType } from "@/components/StoryManagement";
import { useSessionSetup } from '@/PokerPlanning/infrastructure/hooks/useSessionSetup';
import { useStartSession } from '@/PokerPlanning/infrastructure/hooks/useStartSession';

interface Story {
  id: string;
  title: string;
  description: string;
}

export default function SessionSetupStep3() {
  const navigate = useNavigate();
  const { user } = useAuth();
  
  const [sessionName, setSessionName] = useState("");
  const [estimationType, setEstimationType] = useState<EstimationType | null>(
    null,
  );
  const [stories, setStories] = useState<Story[]>([]);
  const [isCreating, setIsCreating] = useState(false);
  const setup = useSessionSetup();
  const startSession = useStartSession();

  useEffect(() => {
    if (setup === undefined) return;
    if (!setup) {
      navigate('/setup/step1');
      return;
    }
    setSessionName(setup.sessionName || "");
    setEstimationType(setup.estimationType || null);
    setStories(setup.stories || []);
  }, [setup, navigate]);

  const handleBack = () => {
    navigate('/setup/step2');
  };

  const handleStartSession = async () => {
    if (!sessionName || !estimationType || stories.length === 0) return;
    setIsCreating(true);
    await startSession(sessionName, estimationType, stories);
  };


  if (!user) {
    navigate(`/login?redirect=${encodeURIComponent('/setup/step3')}`);
    return null;
  }

  const canStart = sessionName && estimationType && stories.length > 0;

  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-muted/20">
      {/* Header */}
      <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center gap-3">
            <Button variant="ghost" size="sm" className="p-2" onClick={handleBack} disabled={isCreating}>
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <h1 className="text-xl font-bold text-foreground">Session Setup</h1>
              <p className="text-xs text-muted-foreground">Step 3 of 3 • Review & Start</p>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-6 max-w-lg">
        <div className="space-y-6">
          {/* Progress */}
          <Card>
            <CardContent className="py-4">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="font-medium">Setup Progress</span>
                  <span className="text-muted-foreground">3 of 3</span>
                </div>
                <Progress value={100} className="h-2" />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span className="text-green-600">✓ Configuration</span>
                  <span className="text-green-600">✓ Stories</span>
                  <span className="font-medium text-primary">Review</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Session Summary */}
          <Card className="border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950/20">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center gap-2 text-green-800 dark:text-green-200">
                <CheckCircle className="h-5 w-5" />
                Setup Complete!
              </CardTitle>
              <CardDescription className="text-green-600 dark:text-green-300">
                Review your session details before starting
              </CardDescription>
            </CardHeader>
          </Card>

          {/* Session Details */}
          <Card>
            <CardHeader className="pb-4">
              <CardTitle className="text-lg flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Session Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">Session Name</p>
                  <p className="text-sm font-semibold">{sessionName}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">Estimation Type</p>
                  <p className="text-sm font-semibold">{estimationType?.name}</p>
                </div>
              </div>
              
              <div className="space-y-1">
                <p className="text-sm font-medium text-muted-foreground">Host</p>
                <div className="flex items-center gap-2">
                  <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center text-xs font-bold text-primary-foreground">
                    {user.name.charAt(0)}
                  </div>
                  <p className="text-sm font-semibold">{user.name}</p>
                </div>
              </div>

              {estimationType && (
                <div className="space-y-2">
                  <p className="text-sm font-medium text-muted-foreground">Estimation Cards</p>
                  <div className="flex flex-wrap gap-1">
                    {estimationType.values.slice(0, 8).map((card, index) => (
                      <div 
                        key={index}
                        className={`w-8 h-8 rounded text-xs font-bold flex items-center justify-center ${card.color} ${card.textColor}`}
                      >
                        {card.icon || card.label}
                      </div>
                    ))}
                    {estimationType.values.length > 8 && (
                      <div className="w-8 h-8 rounded bg-muted flex items-center justify-center text-xs">
                        +{estimationType.values.length - 8}
                      </div>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Stories Summary */}
          <Card>
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Stories to Estimate
                </CardTitle>
                <Badge variant="outline" className="text-sm">
                  {stories.length} {stories.length === 1 ? 'story' : 'stories'}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 max-h-48 overflow-y-auto">
                {stories.map((story, index) => (
                  <div key={story.id} className="border rounded-lg p-3">
                    <div className="flex items-start gap-3">
                      <Badge variant="secondary" className="text-xs mt-0.5">
                        #{index + 1}
                      </Badge>
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-sm leading-tight mb-1">
                          {story.title}
                        </h4>
                        <p className="text-xs text-muted-foreground leading-relaxed line-clamp-2">
                          {story.description}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Start Session */}
          <Card className={canStart ? "border-primary/20 bg-primary/5" : ""}>
            <CardContent className="py-6">
              <div className="text-center space-y-4">
                <div>
                  <h3 className="font-medium text-lg mb-2">Ready to Launch!</h3>
                  <p className="text-sm text-muted-foreground">
                    Your planning session is configured and ready to start
                  </p>
                </div>
                
                <Button
                  onClick={handleStartSession}
                  disabled={!canStart || isCreating}
                  className="w-full h-12 text-base font-medium"
                  size="lg"
                >
                  {isCreating ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2"></div>
                      Creating Session...
                    </>
                  ) : (
                    <>
                      <Play className="h-4 w-4 mr-2" />
                      Start Planning Session
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Navigation */}
          {!isCreating && (
            <div className="flex gap-3">
              <Button 
                onClick={handleBack}
                variant="outline"
                className="flex-1 h-12"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Stories
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
