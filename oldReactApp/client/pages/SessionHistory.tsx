import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  ArrowLeft,
  Calendar,
  Users,
  CheckCircle,
  Clock,
  Hash,
  Plus,
} from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { useRejoinSession } from "@/PokerPlanning/infrastructure/hooks/useRejoinSession";

interface SessionHistoryItem {
  id: string;
  name: string;
  createdAt: string;
  role: "host" | "participant";
  status: "completed" | "active";
  participantCount: number;
  storyCount: number;
  completedStories: number;
}

// Mock data for demonstration
const MOCK_SESSIONS: SessionHistoryItem[] = [
  {
    id: "ABC123",
    name: "Sprint 24 Planning",
    createdAt: "2024-01-15T10:00:00Z",
    role: "host",
    status: "completed",
    participantCount: 4,
    storyCount: 8,
    completedStories: 8,
  },
  {
    id: "DEF456",
    name: "Feature Estimation",
    createdAt: "2024-01-12T14:30:00Z",
    role: "participant",
    status: "completed",
    participantCount: 3,
    storyCount: 5,
    completedStories: 5,
  },
  {
    id: "GHI789",
    name: "Epic Breakdown Session",
    createdAt: "2024-01-10T09:15:00Z",
    role: "host",
    status: "completed",
    participantCount: 6,
    storyCount: 12,
    completedStories: 10,
  },
  {
    id: "JKL012",
    name: "Bug Fix Estimation",
    createdAt: "2024-01-08T16:00:00Z",
    role: "participant",
    status: "completed",
    participantCount: 2,
    storyCount: 3,
    completedStories: 3,
  },
];

export default function SessionHistory() {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [sessions, setSessions] = useState<SessionHistoryItem[]>([]);
  const rejoinSession = useRejoinSession();

  useEffect(() => {
    // In a real app, fetch sessions from API
    setSessions(MOCK_SESSIONS);
  }, []);

  if (!user) {
    navigate("/login?redirect=/history");
    return null;
  }

  const hostedSessions = sessions.filter((s) => s.role === "host");
  const participatedSessions = sessions.filter((s) => s.role === "participant");

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const SessionCard = ({ session }: { session: SessionHistoryItem }) => (
    <Card className="hover:shadow-md transition-shadow">
      <CardContent className="p-4">
        <div className="space-y-3">
          <div className="flex items-start justify-between">
            <div className="space-y-1">
              <h3 className="font-medium text-sm leading-tight">
                {session.name}
              </h3>
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                <Calendar className="h-3 w-3" />
                {formatDate(session.createdAt)}
              </div>
            </div>
            <Badge variant="outline" className="text-xs">
              {session.id}
            </Badge>
          </div>

          <div className="grid grid-cols-2 gap-3 text-xs">
            <div className="flex items-center gap-1">
              <Users className="h-3 w-3 text-muted-foreground" />
              <span>{session.participantCount} participants</span>
            </div>
            <div className="flex items-center gap-1">
              <CheckCircle className="h-3 w-3 text-muted-foreground" />
              <span>
                {session.completedStories}/{session.storyCount} stories
              </span>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Badge
                variant={session.role === "host" ? "default" : "secondary"}
                className="text-xs"
              >
                {session.role === "host" ? "Hosted" : "Participated"}
              </Badge>
              <Badge
                variant={session.status === "completed" ? "outline" : "default"}
                className="text-xs"
              >
                {session.status === "completed" ? "Completed" : "Active"}
              </Badge>
            </div>

            {session.status === "active" && (
              <Button
                size="sm"
                variant="outline"
                className="text-xs h-7"
                onClick={() => rejoinSession(session.id, session.status)}
              >
                Rejoin
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-muted/20">
      {/* Header */}
      <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center gap-3">
            <Link to="/">
              <Button variant="ghost" size="sm" className="p-2">
                <ArrowLeft className="h-4 w-4" />
              </Button>
            </Link>
            <div className="relative">
              <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                <Hash className="h-4 w-4 text-primary-foreground" />
              </div>
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-accent rounded-full animate-pulse"></div>
            </div>
            <div>
              <h1 className="text-xl font-bold text-foreground">
                Session History
              </h1>
              <p className="text-xs text-muted-foreground">
                Your planning sessions
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-6 space-y-6">
        {/* Quick Action */}
        <Card className="border-primary/20 bg-primary/5">
          <CardContent className="py-4">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="font-medium text-sm mb-1">Ready to Plan?</h2>
                <p className="text-xs text-muted-foreground">
                  Start a new estimation session
                </p>
              </div>
              <Button size="sm" asChild>
                <Link to="/setup/step1">
                  <Plus className="h-3 w-3 mr-1" />
                  New Session
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Hosted Sessions */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold">Sessions You Hosted</h2>
            <Badge variant="outline" className="text-xs">
              {hostedSessions.length} sessions
            </Badge>
          </div>

          {hostedSessions.length === 0 ? (
            <Card>
              <CardContent className="py-8 text-center">
                <Clock className="h-8 w-8 mx-auto mb-3 text-muted-foreground opacity-50" />
                <p className="text-sm text-muted-foreground mb-2">
                  No hosted sessions yet
                </p>
                <Button size="sm" variant="outline" asChild>
                  <Link to="/setup/step1">Create Your First Session</Link>
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="grid gap-3">
              {hostedSessions.map((session) => (
                <SessionCard key={session.id} session={session} />
              ))}
            </div>
          )}
        </div>

        {/* Participated Sessions */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold">Sessions You Joined</h2>
            <Badge variant="outline" className="text-xs">
              {participatedSessions.length} sessions
            </Badge>
          </div>

          {participatedSessions.length === 0 ? (
            <Card>
              <CardContent className="py-8 text-center">
                <Users className="h-8 w-8 mx-auto mb-3 text-muted-foreground opacity-50" />
                <p className="text-sm text-muted-foreground">
                  No participated sessions yet
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid gap-3">
              {participatedSessions.map((session) => (
                <SessionCard key={session.id} session={session} />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
