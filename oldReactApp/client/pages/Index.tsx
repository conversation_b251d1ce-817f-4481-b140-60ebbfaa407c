import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import {
  Users,
  Zap,
  Target,
  ChevronRight,
  Plus,
  Hash,
  LogIn,
  UserPlus,
  LogOut,
  User,
  History,
  ChevronDown,
} from "lucide-react";
import { Link } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { useCreateSession } from "@/PokerPlanning/infrastructure/hooks/useCreateSession";
import { useJoinSession } from "@/PokerPlanning/infrastructure/hooks/useJoinSession";
import { useSignOut } from "@/PokerPlanning/infrastructure/hooks/useSignOut";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

export default function Index() {
  const { user } = useAuth();
  const createSession = useCreateSession();
  const joinSession = useJoinSession();
  const signOut = useSignOut();
  const [sessionCode, setSessionCode] = useState("");
  const [userName, setUserName] = useState("");

  const features = [
    {
      icon: <Zap className="h-5 w-5" />,
      title: "Fast Voting",
      description: "Quick and intuitive card selection",
    },
    {
      icon: <Users className="h-5 w-5" />,
      title: "Team Sync",
      description: "Real-time collaboration with your team",
    },
    {
      icon: <Target className="h-5 w-5" />,
      title: "Accurate Estimates",
      description: "Fibonacci-based planning poker",
    },
  ];

  const generateSessionId = () => {
    return Math.random().toString(36).substring(2, 8).toUpperCase();
  };

  const handleJoinSession = () => {
    const name = user ? undefined : userName;
    joinSession(sessionCode, name);
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-muted/20">
      {/* Header */}
      <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="relative">
                <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                  <Hash className="h-4 w-4 text-primary-foreground" />
                </div>
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-accent rounded-full animate-pulse"></div>
              </div>
              <div>
                <h1 className="text-xl font-bold text-foreground">
                  Poker Planning
                </h1>
                <p className="text-xs text-muted-foreground">
                  Estimation made simple
                </p>
              </div>
            </div>

            {/* User Menu */}
            <div className="flex items-center gap-2">
              {user ? (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      className="flex items-center gap-2 px-3 py-1.5 h-auto"
                    >
                      <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center text-xs font-bold text-primary-foreground">
                        {user.name.charAt(0)}
                      </div>
                      <span className="text-sm font-medium">{user.name}</span>
                      <ChevronDown className="h-3 w-3 opacity-50" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-48">
                    <DropdownMenuItem asChild>
                      <Link
                        to="/history"
                        className="flex items-center gap-2 w-full cursor-pointer"
                      >
                        <History className="h-4 w-4" />
                        Session History
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      onClick={signOut}
                      className="flex items-center gap-2 cursor-pointer"
                    >
                      <LogOut className="h-4 w-4" />
                      Sign Out
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              ) : (
                <Link to="/login">
                  <Button variant="ghost" size="sm" className="text-xs">
                    <LogIn className="h-3 w-3 mr-1" />
                    Sign In
                  </Button>
                </Link>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8 space-y-8">
        {/* Hero Section */}
        <div className="text-center space-y-4">
          <div className="space-y-2">
            <Badge variant="secondary" className="mb-4">
              <Zap className="h-3 w-3 mr-1" />
              Fast & Free
            </Badge>
            <h2 className="text-3xl font-bold tracking-tight text-foreground">
              Poker Planning,
              <span className="text-primary block">Simplified</span>
            </h2>
            <p className="text-muted-foreground text-base leading-relaxed max-w-md mx-auto">
              Estimate user stories with your team using poker planning. Fast,
              intuitive, and mobile-optimized.
            </p>
          </div>

          {/* Features */}
          <div className="grid grid-cols-1 gap-4 mt-8">
            {features.map((feature, index) => (
              <div
                key={index}
                className="flex items-center gap-3 p-3 rounded-lg bg-card border"
              >
                <div className="flex-shrink-0 w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center text-primary">
                  {feature.icon}
                </div>
                <div className="text-left">
                  <h3 className="font-medium text-sm text-foreground">
                    {feature.title}
                  </h3>
                  <p className="text-xs text-muted-foreground">
                    {feature.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Session Actions */}
        <div className="space-y-6">
          {/* Create Session - For authenticated users */}
          {user && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center gap-2">
                  <Plus className="h-5 w-5 text-primary" />
                  Create New Session
                </CardTitle>
                <CardDescription className="text-sm">
                  Set up a new planning session for your team
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button
                  onClick={createSession}
                  className="w-full h-12 text-base font-medium touch-manipulation active:scale-98"
                  size="lg"
                >
                  Start Setup
                  <ChevronRight className="h-4 w-4 ml-1" />
                </Button>
              </CardContent>
            </Card>
          )}

          {/* Sign up prompt for non-authenticated users */}
          {!user && (
            <Card className="border-primary/20 bg-primary/5">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center gap-2">
                  <Plus className="h-5 w-5 text-primary" />
                  Create Sessions
                </CardTitle>
                <CardDescription className="text-sm">
                  Sign up to create and host planning sessions
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button
                  onClick={createSession}
                  className="w-full h-12 text-base font-medium touch-manipulation active:scale-98"
                  size="lg"
                >
                  Sign In to Create
                  <ChevronRight className="h-4 w-4 ml-1" />
                </Button>
              </CardContent>
            </Card>
          )}

          {/* Join Session - Streamlined */}
          <Card>
            <CardHeader className="pb-4">
              <CardTitle className="text-lg flex items-center gap-2">
                <Users className="h-5 w-5 text-accent" />
                Join Session
              </CardTitle>
              <CardDescription className="text-sm">
                Enter a session code to join your team
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Session Code - Always first */}
              <div className="space-y-2">
                <Label htmlFor="sessionCode" className="text-sm font-medium">
                  Session Code
                </Label>
                <Input
                  id="sessionCode"
                  type="text"
                  placeholder="ABC123"
                  value={sessionCode}
                  onChange={(e) => setSessionCode(e.target.value.toUpperCase())}
                  className="h-12 text-center text-lg font-mono tracking-widest placeholder:text-sm placeholder:tracking-normal"
                  maxLength={6}
                />
              </div>

              {/* Name input - only for guests, auto-show when code entered */}
              {!user && sessionCode.trim() && (
                <div className="space-y-2 animate-in slide-in-from-top-2 duration-300">
                  <Label htmlFor="userName" className="text-sm font-medium">
                    Your Name
                  </Label>
                  <Input
                    id="userName"
                    type="text"
                    placeholder="Enter your name"
                    value={userName}
                    onChange={(e) => setUserName(e.target.value)}
                    className="h-12 text-base"
                    autoFocus
                  />
                </div>
              )}

              {/* Action Buttons */}
              {sessionCode.trim() && (
                <div className="space-y-3 animate-in slide-in-from-top-2 duration-300">
                  {user ? (
                    /* Authenticated user - simple join */
                    <Button
                      onClick={handleJoinSession}
                      className="w-full h-12 text-base font-medium touch-manipulation active:scale-98"
                      size="lg"
                    >
                      Join Session
                      <ChevronRight className="h-4 w-4 ml-1" />
                    </Button>
                  ) : (
                    /* Guest user - two options */
                    <>
                      <Button
                        onClick={handleJoinSession}
                        disabled={!userName.trim()}
                        className="w-full h-12 text-base font-medium touch-manipulation active:scale-98"
                        size="lg"
                      >
                        Join as {userName.trim() || "Guest"}
                        <ChevronRight className="h-4 w-4 ml-1" />
                      </Button>

                      <div className="relative">
                        <div className="absolute inset-0 flex items-center">
                          <span className="w-full border-t" />
                        </div>
                        <div className="relative flex justify-center text-xs uppercase">
                          <span className="bg-background px-2 text-muted-foreground">
                            or
                          </span>
                        </div>
                      </div>

                      <Link
                        to={`/login?redirect=${encodeURIComponent(`/room/${sessionCode}`)}`}
                      >
                        <Button
                          variant="outline"
                          className="w-full h-11 text-sm font-medium"
                        >
                          <LogIn className="h-4 w-4 mr-2" />
                          Sign In & Join
                        </Button>
                      </Link>
                    </>
                  )}
                </div>
              )}

              {/* Helper text */}
              {!sessionCode.trim() && (
                <div className="text-center py-2">
                  <p className="text-xs text-muted-foreground">
                    Get the session code from your team lead
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Footer */}
        <div className="text-center pt-8 pb-4">
          <p className="text-xs text-muted-foreground">
            Proposed by cleaner-code.com
          </p>
        </div>
      </div>
    </div>
  );
}
