import { LogMissingRouteCommandHandler } from '@/Diagnostics/application/commands/logMissingRoute/logMissingRouteCommandHandler';
import { ConsoleDiagnosticsGateway } from '@/Diagnostics/infrastructure/gateways/ConsoleDiagnosticsGateway';

export function useLogMissingRoute() {
  const diagnosticsGateway = new ConsoleDiagnosticsGateway();
  const handler = new LogMissingRouteCommandHandler(diagnosticsGateway);
  return (path: string) => handler.execute({ path });
}
