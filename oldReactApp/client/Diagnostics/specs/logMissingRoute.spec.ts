import { describe, it, expect } from 'vitest';
import { LogMissingRouteCommandHandler } from '@/Diagnostics/application/commands/logMissingRoute/logMissingRouteCommandHandler';
import type { DiagnosticsGateway } from '@/Diagnostics/application/gateways/DiagnosticsGateway';

describe("When logging a missing route", () => {
  it("should send the missing route to diagnostics", async () => {
    // Arrange
    const diagnosticsGateway = new FakeDiagnosticsGateway();
    const handler = new LogMissingRouteCommandHandler(diagnosticsGateway);
    // Act
    await handler.execute({ path: '/unknown' });
    // Assert
    expect(diagnosticsGateway.loggedPath).toBe('/unknown');
  });
});

class FakeDiagnosticsGateway implements DiagnosticsGateway {
  loggedPath?: string;
  logMissingRoute(path: string): void {
    this.loggedPath = path;
  }
}
