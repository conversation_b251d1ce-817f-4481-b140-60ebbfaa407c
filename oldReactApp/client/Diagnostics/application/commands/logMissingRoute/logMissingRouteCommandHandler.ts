import type { DiagnosticsGateway } from '@/Diagnostics/application/gateways/DiagnosticsGateway';
import type { LogMissingRouteCommand } from '@/Diagnostics/application/commands/logMissingRoute/logMissingRouteCommand';

export class LogMissingRouteCommandHandler {
  constructor(private readonly diagnosticsGateway: DiagnosticsGateway) {}

  async execute(command: LogMissingRouteCommand): Promise<void> {
    this.diagnosticsGateway.logMissingRoute(command.path);
  }
}
