import type { AuthGateway } from '@/PokerPlanning/application/gateways/AuthGateway';
import type {
  SessionGateway,
  SessionSummary,
} from '@/PokerPlanning/application/gateways/SessionGateway';

export type SessionHistory = {
  hosted: SessionSummary[];
  participated: SessionSummary[];
};

export class GetSessionHistoryQueryHandler {
  constructor(
    private readonly authGateway: AuthGateway,
    private readonly sessionGateway: SessionGateway,
  ) {}

  async execute(): Promise<SessionHistory> {
    const user = this.authGateway.getCurrentUser();
    if (!user) {
      return { hosted: [], participated: [] };
    }
    const sessions = await this.sessionGateway.getSessionsForUser(user.id);
    return {
      hosted: sessions.filter((s) => s.role === 'host'),
      participated: sessions.filter((s) => s.role === 'participant'),
    };
  }
}
