export interface Participant {
  id: string;
  vote: string | null;
}

export interface VotingStats {
  min: number;
  max: number;
  avg: number;
  votes: string[];
  highest: Participant[];
  lowest: Participant[];
}

export interface GetVotingStatsQuery {
  participants: Participant[];
}

export class GetVotingStatsQueryHandler {
  execute({ participants }: GetVotingStatsQuery): VotingStats | null {
    const votes = participants.filter(p => p.vote).map(p => p.vote!);
    const numericVotes = votes
      .filter(v => !isNaN(Number(v)))
      .map(v => Number(v));
    if (numericVotes.length === 0) return null;
    const min = Math.min(...numericVotes);
    const max = Math.max(...numericVotes);
    const avg =
      Math.round(
        (numericVotes.reduce((a, b) => a + b, 0) / numericVotes.length) * 10,
      ) / 10;
    const highest = participants.filter(p => p.vote === max.toString());
    const lowest = participants.filter(p => p.vote === min.toString());
    return { min, max, avg, votes, highest, lowest };
  }
}
