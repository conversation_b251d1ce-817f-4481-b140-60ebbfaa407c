import type { NavigationGateway } from '@/PokerPlanning/application/gateways/NavigationGateway';
import type { JoinSessionCommand } from './joinSessionCommand';

export class JoinSessionCommandHandler {
  constructor(private readonly navigationGateway: NavigationGateway) {}

  async execute({ sessionCode, participantName }: JoinSessionCommand) {
    if (!sessionCode.trim() || !participantName.trim()) {
      return;
    }
    this.navigationGateway.navigateTo(
      `/room/${sessionCode}?name=${encodeURIComponent(participantName)}`,
    );
  }
}
