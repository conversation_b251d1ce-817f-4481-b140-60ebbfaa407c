import type { StoryStore } from '@/PokerPlanning/application/stores/storyStore';
import type { UiStore } from '@/PokerPlanning/application/stores/uiStore';

export class NextStoryCommandHandler {
  constructor(
    private readonly storyStore: StoryStore,
    private readonly uiStore: UiStore,
  ) {}

  execute(): void {
    const moved = this.storyStore.moveToNextStory();
    if (!moved) return;
    this.uiStore.hideAllVotes();
    this.uiStore.deselectCard();
    this.uiStore.showVotingCards();
  }
}
