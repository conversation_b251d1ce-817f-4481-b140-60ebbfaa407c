import type { NavigationGateway } from '@/PokerPlanning/application/gateways/NavigationGateway';
import type { SessionSetupRepository } from '@/PokerPlanning/application/repositories/SessionSetupRepository';
import type { ConfigureSessionCommand } from './configureSessionCommand';

export class ConfigureSessionCommandHandler {
  constructor(
    private readonly sessionSetupRepository: SessionSetupRepository,
    private readonly navigationGateway: NavigationGateway,
  ) {}

  async execute(command: ConfigureSessionCommand): Promise<void> {
    if (!command.sessionName.trim() || command.estimationType.values.length < 3) {
      return;
    }
    await this.sessionSetupRepository.save({
      sessionName: command.sessionName.trim(),
      estimationType: command.estimationType,
      step: 1,
    });
    this.navigationGateway.navigateTo(
      `/setup/step2?sessionName=${encodeURIComponent(command.sessionName.trim())}`,
    );
  }
}
