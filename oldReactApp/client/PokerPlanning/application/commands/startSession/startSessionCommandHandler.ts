import type { NavigationGateway } from '@/PokerPlanning/application/gateways/NavigationGateway';
import type { SessionRepository } from '@/PokerPlanning/application/repositories/SessionRepository';
import type { StartSessionCommand } from './startSessionCommand';

export class StartSessionCommandHandler {
  constructor(
    private readonly sessionRepository: SessionRepository,
    private readonly navigationGateway: NavigationGateway,
  ) {}

  async execute(command: StartSessionCommand): Promise<void> {
    if (!command.sessionName.trim() || command.stories.length === 0) {
      return;
    }
    const sessionId = await this.sessionRepository.save({
      sessionName: command.sessionName,
      estimationType: command.estimationType,
      stories: command.stories,
      createdBy: command.hostName,
    });
    this.navigationGateway.navigateTo(
      `/room/${sessionId}?name=${encodeURIComponent(command.hostName)}&sessionName=${encodeURIComponent(command.sessionName)}&host=true`,
    );
  }
}
