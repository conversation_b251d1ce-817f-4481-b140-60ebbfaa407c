export interface EstimationCard {
  value: string;
  label: string;
  color: string;
  textColor: string;
}

export interface EstimationType {
  id: string;
  name: string;
  values: EstimationCard[];
}

export interface Story {
  id: string;
  title: string;
  description: string;
}

export interface StartSessionCommand {
  sessionName: string;
  estimationType: EstimationType;
  stories: Story[];
  hostName: string;
}
