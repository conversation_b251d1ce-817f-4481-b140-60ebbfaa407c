import type { SessionRepository } from '@/PokerPlanning/application/repositories/SessionRepository';
import type { UiStore } from '@/PokerPlanning/application/stores/uiStore';
import type { ChangeEstimationTypeCommand } from './changeEstimationTypeCommand';

export class ChangeEstimationTypeCommandHandler {
  constructor(
    private readonly sessionRepository: SessionRepository,
    private readonly uiStore: UiStore,
  ) {}

  async execute(command: ChangeEstimationTypeCommand): Promise<void> {
    if (!command.sessionId.trim()) return;
    await this.sessionRepository.updateEstimationType(
      command.sessionId,
      command.estimationType,
    );
    this.uiStore.hideAllVotes();
    this.uiStore.deselectCard();
    this.uiStore.showVotingCards();
  }
}
