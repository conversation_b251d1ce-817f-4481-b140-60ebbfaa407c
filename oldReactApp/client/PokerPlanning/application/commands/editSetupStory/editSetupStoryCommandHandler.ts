import type { StoryStore } from '@/PokerPlanning/application/stores/storyStore';
import type { SessionSetupRepository } from '@/PokerPlanning/application/repositories/SessionSetupRepository';
import type { EditSetupStoryCommand } from './editSetupStoryCommand';

export class EditSetupStoryCommandHandler {
  constructor(
    private readonly storyStore: StoryStore,
    private readonly sessionSetupRepository: SessionSetupRepository,
  ) {}

  async execute(command: EditSetupStoryCommand): Promise<void> {
    if (!command.storyId.trim() || !command.title.trim()) {
      return;
    }
    this.storyStore.updateStory(command.storyId, {
      title: command.title,
      description: command.description,
    });
    const stories = this.storyStore.getStories();
    await this.sessionSetupRepository.save({ stories, step: 2 });
  }
}
