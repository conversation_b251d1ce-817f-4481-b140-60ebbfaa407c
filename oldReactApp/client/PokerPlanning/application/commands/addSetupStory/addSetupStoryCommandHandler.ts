import type { StoryStore } from '@/PokerPlanning/application/stores/storyStore';
import type { SessionSetupRepository } from '@/PokerPlanning/application/repositories/SessionSetupRepository';
import type { AddSetupStoryCommand } from './addSetupStoryCommand';

export class AddSetupStoryCommandHandler {
  constructor(
    private readonly storyStore: StoryStore,
    private readonly sessionSetupRepository: SessionSetupRepository,
  ) {}

  async execute(command: AddSetupStoryCommand): Promise<void> {
    if (!command.title.trim()) {
      return;
    }
    this.storyStore.addStory({
      id: Date.now().toString(),
      title: command.title,
      description: command.description,
      status: 'pending',
    });
    const stories = this.storyStore.getStories();
    await this.sessionSetupRepository.save({ stories, step: 2 });
  }
}
