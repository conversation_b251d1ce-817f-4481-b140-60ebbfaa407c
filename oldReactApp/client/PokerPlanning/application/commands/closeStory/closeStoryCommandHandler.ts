import type { StoryStore } from '@/PokerPlanning/application/stores/storyStore';
import type { UiStore } from '@/PokerPlanning/application/stores/uiStore';
import type { CloseStoryCommand } from './closeStoryCommand';

export class CloseStoryCommandHandler {
  constructor(
    private readonly storyStore: StoryStore,
    private readonly uiStore: UiStore,
  ) {}

  execute({ finalEstimate }: CloseStoryCommand): void {
    this.storyStore.closeCurrentStory(finalEstimate);
    this.uiStore.hideAllVotes();
    this.uiStore.deselectCard();
    this.uiStore.showVotingCards();
  }
}
