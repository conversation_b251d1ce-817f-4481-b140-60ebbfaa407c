import type { StoryStore } from '@/PokerPlanning/application/stores/storyStore';
import type { EditStoryCommand } from './editStoryCommand';

export class EditStoryCommandHandler {
  constructor(private readonly storyStore: StoryStore) {}

  async execute(command: EditStoryCommand): Promise<void> {
    this.storyStore.updateStory(command.id, {
      title: command.title,
      description: command.description,
    });
  }
}
