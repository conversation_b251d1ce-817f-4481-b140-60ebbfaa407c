import type { ClipboardGateway } from '@/PokerPlanning/application/gateways/ClipboardGateway';
import type { CopySessionCodeCommand } from './copySessionCodeCommand';

export class CopySessionCodeCommandHandler {
  constructor(private readonly clipboardGateway: ClipboardGateway) {}

  async execute({ sessionCode }: CopySessionCodeCommand): Promise<void> {
    await this.clipboardGateway.copy(sessionCode);
  }
}
