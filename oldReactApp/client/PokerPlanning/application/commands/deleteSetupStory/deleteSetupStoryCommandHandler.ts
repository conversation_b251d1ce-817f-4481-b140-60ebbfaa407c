import type { StoryStore } from '@/PokerPlanning/application/stores/storyStore';
import type { SessionSetupRepository } from '@/PokerPlanning/application/repositories/SessionSetupRepository';
import type { DeleteSetupStoryCommand } from './deleteSetupStoryCommand';

export class DeleteSetupStoryCommandHandler {
  constructor(
    private readonly storyStore: StoryStore,
    private readonly sessionSetupRepository: SessionSetupRepository,
  ) {}

  async execute(command: DeleteSetupStoryCommand): Promise<void> {
    if (!command.storyId.trim()) {
      return;
    }
    this.storyStore.deleteStory(command.storyId);
    const stories = this.storyStore.getStories();
    await this.sessionSetupRepository.save({ stories, step: 2 });
  }
}
