import type { StoryStore, Story } from '@/PokerPlanning/application/stores/storyStore';
import type { AddStoryCommand } from './addStoryCommand';

export class AddStoryCommandHandler {
  constructor(private readonly storyStore: StoryStore) {}

  async execute(command: AddStoryCommand): Promise<void> {
    const story: Story = {
      id: Date.now().toString(),
      title: command.title,
      description: command.description,
      status: 'pending',
    };
    this.storyStore.addStory(story);
  }
}
