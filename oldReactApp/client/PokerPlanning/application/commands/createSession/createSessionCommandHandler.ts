import type { AuthGateway } from '@/PokerPlanning/application/gateways/AuthGateway';
import type { NavigationGateway } from '@/PokerPlanning/application/gateways/NavigationGateway';

export class CreateSessionCommandHandler {
  constructor(
    private readonly authGateway: AuthGateway,
    private readonly navigationGateway: NavigationGateway,
  ) {}

  async execute(): Promise<void> {
    const user = this.authGateway.getCurrentUser();
    if (user) {
      this.navigationGateway.navigateTo('/setup/step1');
    } else {
      this.navigationGateway.navigateTo(
        `/login?redirect=${encodeURIComponent('/setup/step1')}`,
      );
    }
  }
}
