import type { NavigationGateway } from '@/PokerPlanning/application/gateways/NavigationGateway';
import type { RejoinSessionCommand } from './rejoinSessionCommand';

export class RejoinSessionCommandHandler {
  constructor(private readonly navigationGateway: NavigationGateway) {}

  execute(command: RejoinSessionCommand): void {
    if (command.status === 'active') {
      this.navigationGateway.navigateTo(`/room/${command.sessionId}`);
    }
  }
}
