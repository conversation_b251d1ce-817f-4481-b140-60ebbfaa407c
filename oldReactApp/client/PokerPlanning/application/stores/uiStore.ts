import type { StoreApi } from 'zustand/vanilla';

export interface UiState {
  selectedCard: string | null;
  votesRevealed: boolean;
  votingCardsVisible: boolean;
}

export interface UiStore extends StoreApi<UiState> {
  selectCard(card: string): void;
  deselectCard(): void;
  toggleVotesRevealed(): void;
  hideAllVotes(): void;
  showVotingCards(): void;
  hideVotingCards(): void;
  getSelectedCard(): string | null;
  areVotesRevealed(): boolean;
  areVotingCardsVisible(): boolean;
}
