import type { StoreApi } from 'zustand/vanilla';

export interface Story {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'active' | 'completed';
  finalEstimate?: string;
  votes?: Record<string, string>;
}

export interface StoryState {
  stories: Story[];
  currentStoryIndex: number;
}

export interface StoryStore extends StoreApi<StoryState> {
  addStory(story: Story): void;
  updateStory(storyId: string, updates: { title: string; description: string }): void;
  deleteStory(storyId: string): void;
  moveToNextStory(): boolean;
  closeCurrentStory(finalEstimate: string): void;
  getStories(): Story[];
}
