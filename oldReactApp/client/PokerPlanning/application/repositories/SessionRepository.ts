import type { EstimationType, Story } from '@/PokerPlanning/application/commands/startSession/startSessionCommand';

export interface SessionData {
  sessionName: string;
  estimationType: EstimationType;
  stories: Story[];
  createdBy: string;
}

export interface SessionRepository {
  save(session: SessionData): Promise<string>;
  updateEstimationType(
    sessionId: string,
    estimationType: EstimationType,
  ): Promise<void>;
}
