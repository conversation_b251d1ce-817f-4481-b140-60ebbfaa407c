import { describe, it, expect } from 'vitest';
import { CreateSessionCommandHandler } from '@/PokerPlanning/application/commands/createSession/createSessionCommandHandler';
import type { AuthGateway } from '@/PokerPlanning/application/gateways/AuthGateway';
import type { NavigationGateway } from '@/PokerPlanning/application/gateways/NavigationGateway';

describe("When creating a session", () => {
  it("should navigate authenticated users to session setup", async () => {
    // Arrange
    const authGateway: AuthGateway = {
      getCurrentUser: () => ({ id: '1', name: '<PERSON>' }),
      signOut: () => {},
    };
    const navigationGateway = new FakeNavigationGateway();
    const handler = new CreateSessionCommandHandler(authGateway, navigationGateway);
    // Act
    await handler.execute();
    // Assert
    expect(navigationGateway.navigatedTo).toBe('/setup/step1');
  });

  it("should redirect guests to login with return URL", async () => {
    // Arrange
    const authGateway: AuthGateway = {
      getCurrentUser: () => null,
      signOut: () => {},
    };
    const navigationGateway = new FakeNavigationGateway();
    const handler = new CreateSessionCommandHandler(authGateway, navigationGateway);
    // Act
    await handler.execute();
    // Assert
    expect(navigationGateway.navigatedTo).toBe('/login?redirect=%2Fsetup%2Fstep1');
  });
});

class FakeNavigationGateway implements NavigationGateway {
  navigatedTo?: string;
  navigateTo(path: string): void {
    this.navigatedTo = path;
  }
}
