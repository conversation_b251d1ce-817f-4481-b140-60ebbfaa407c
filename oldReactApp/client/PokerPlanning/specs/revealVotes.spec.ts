import { describe, it, expect } from 'vitest';
import { RevealVotesCommandHandler } from '@/PokerPlanning/application/commands/revealVotes/revealVotesCommandHandler';
import { UiStore } from '@/PokerPlanning/infrastructure/stores/uiStore';

describe("When revealing votes", () => {
  it("should reveal votes when currently hidden", async () => {
    // Arrange
    const uiStore = new UiStore();
    uiStore.setState({ votesRevealed: false });
    const handler = new RevealVotesCommandHandler(uiStore);
    // Act
    await handler.execute();
    // Assert
    expect(uiStore.areVotesRevealed()).toBe(true);
  });

  it("should hide votes when currently revealed", async () => {
    // Arrange
    const uiStore = new UiStore();
    uiStore.setState({ votesRevealed: true });
    const handler = new RevealVotesCommandHandler(uiStore);
    // Act
    await handler.execute();
    // Assert
    expect(uiStore.areVotesRevealed()).toBe(false);
  });
});
