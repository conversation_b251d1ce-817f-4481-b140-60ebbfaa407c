import { describe, it, expect } from 'vitest';
import { EndSessionCommandHandler } from '@/PokerPlanning/application/commands/endSession/endSessionCommandHandler';
import type { NavigationGateway } from '@/PokerPlanning/application/gateways/NavigationGateway';

class FakeNavigationGateway implements NavigationGateway {
  navigatedTo?: string;
  navigateTo(path: string): void {
    this.navigatedTo = path;
  }
}

describe("When ending a session", () => {
  it("should navigate to home", async () => {
    // Arrange
    const navigationGateway = new FakeNavigationGateway();
    const handler = new EndSessionCommandHandler(navigationGateway);
    // Act
    await handler.execute();
    // Assert
    expect(navigationGateway.navigatedTo).toBe("/");
  });
});
