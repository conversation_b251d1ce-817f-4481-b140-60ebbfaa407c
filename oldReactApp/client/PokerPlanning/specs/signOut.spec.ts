import { describe, it, expect } from 'vitest';
import { SignOutCommandHandler } from '@/PokerPlanning/application/commands/signOut/signOutCommandHandler';
import type { AuthGateway } from '@/PokerPlanning/application/gateways/AuthGateway';

class FakeAuthGateway implements AuthGateway {
  constructor(public signedOut = false) {}
  getCurrentUser() {
    return { id: "1", name: "<PERSON>" };
  }
  signOut() {
    this.signedOut = true;
  }
}

describe("When signing out", () => {
  it("should terminate the current session", async () => {
    // Arrange
    const authGateway = new FakeAuthGateway();
    const handler = new SignOutCommandHandler(authGateway);
    // Act
    await handler.execute();
    // Assert
    expect(authGateway.signedOut).toBe(true);
  });
});
