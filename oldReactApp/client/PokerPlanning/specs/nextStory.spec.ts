import { describe, it, expect } from 'vitest';
import { NextStoryCommandHandler } from '@/PokerPlanning/application/commands/nextStory/nextStoryCommandHandler';
import { StoryStore } from '@/PokerPlanning/infrastructure/stores/storyStore';
import { UiStore } from '@/PokerPlanning/infrastructure/stores/uiStore';
import type { Story } from '@/PokerPlanning/application/stores/storyStore';

describe("When moving to the next story", () => {
  it("should complete current story and activate next story while resetting votes", () => {
    // Arrange
    const initialStories: Story[] = [
      { id: "1", title: "Story 1", description: "Desc 1", status: "active", votes: { u1: "3" } },
      { id: "2", title: "Story 2", description: "Desc 2", status: "pending" },
    ];
    const storyStore = new StoryStore(initialStories);
    const uiStore = new UiStore();
    uiStore.setState({ selectedCard: "5", votesRevealed: true, votingCardsVisible: false });
    const handler = new NextStoryCommandHandler(storyStore, uiStore);
    // Act
    handler.execute();
    // Assert
    const { currentStoryIndex, stories } = storyStore.getState();
    expect(currentStoryIndex).toBe(1);
    expect(stories[0].status).toBe("completed");
    expect(stories[1].status).toBe("active");
    expect(uiStore.getSelectedCard()).toBeNull();
    expect(uiStore.areVotesRevealed()).toBe(false);
    expect(uiStore.areVotingCardsVisible()).toBe(true);
  });

  it("should keep current story when there is no next story", () => {
    // Arrange
    const initialStories: Story[] = [
      { id: "1", title: "Story 1", description: "Desc 1", status: "active" },
    ];
    const storyStore = new StoryStore(initialStories);
    const uiStore = new UiStore();
    uiStore.setState({ selectedCard: "5", votesRevealed: true, votingCardsVisible: false });
    const handler = new NextStoryCommandHandler(storyStore, uiStore);
    // Act
    handler.execute();
    // Assert
    const { currentStoryIndex, stories } = storyStore.getState();
    expect(currentStoryIndex).toBe(0);
    expect(stories[0].status).toBe("active");
    expect(uiStore.getSelectedCard()).toBe("5");
    expect(uiStore.areVotesRevealed()).toBe(true);
    expect(uiStore.areVotingCardsVisible()).toBe(false);
  });
});
