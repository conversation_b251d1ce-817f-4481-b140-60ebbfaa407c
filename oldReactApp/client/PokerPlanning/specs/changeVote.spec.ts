import { describe, it, expect } from 'vitest';
import { ChangeVoteCommandHandler } from '@/PokerPlanning/application/commands/changeVote/changeVoteCommandHandler';
import { UiStore } from '@/PokerPlanning/infrastructure/stores/uiStore';

describe("When changing a vote", () => {
  it("should show voting cards and hide votes", async () => {
    // Arrange
    const uiStore = new UiStore();
    uiStore.setState({ votesRevealed: true, votingCardsVisible: false });
    const handler = new ChangeVoteCommandHandler(uiStore);
    // Act
    await handler.execute();
    // Assert
    expect(uiStore.areVotingCardsVisible()).toBe(true);
    expect(uiStore.areVotesRevealed()).toBe(false);
  });
});
