import { describe, it, expect } from 'vitest';
import { ResetVotesCommandHandler } from '@/PokerPlanning/application/commands/resetVotes/resetVotesCommandHandler';
import { UiStore } from '@/PokerPlanning/infrastructure/stores/uiStore';

describe("When resetting votes", () => {
  it("should clear selection and hide votes", async () => {
    // Arrange
    const uiStore = new UiStore();
    uiStore.setState({
      selectedCard: "5",
      votesRevealed: true,
      votingCardsVisible: false,
    });
    const handler = new ResetVotesCommandHandler(uiStore);
    // Act
    await handler.execute();
    // Assert
    expect(uiStore.areVotesRevealed()).toBe(false);
    expect(uiStore.getSelectedCard()).toBeNull();
    expect(uiStore.areVotingCardsVisible()).toBe(true);
  });
});
