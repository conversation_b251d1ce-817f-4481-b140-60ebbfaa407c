import { describe, it, expect } from 'vitest';
import { ConfigureSessionCommandHandler } from '@/PokerPlanning/application/commands/configureSession/configureSessionCommandHandler';
import type { ConfigureSessionCommand } from '@/PokerPlanning/application/commands/configureSession/configureSessionCommand';
import type { NavigationGateway } from '@/PokerPlanning/application/gateways/NavigationGateway';
import type {
  SessionSetupRepository,
  SessionSetupData,
} from '@/PokerPlanning/application/repositories/SessionSetupRepository';

class FakeSessionSetupRepository implements SessionSetupRepository {
  saved?: SessionSetupData;
  async save(data: SessionSetupData): Promise<void> {
    this.saved = data;
  }
  async get(): Promise<SessionSetupData | null> {
    return this.saved ?? null;
  }
}

class FakeNavigationGateway implements NavigationGateway {
  navigatedTo?: string;
  navigateTo(path: string): void {
    this.navigatedTo = path;
  }
}

describe("When configuring session", () => {
  it("should persist configuration and navigate to story setup", async () => {
    // Arrange
    const repository = new FakeSessionSetupRepository();
    const navigationGateway = new FakeNavigationGateway();
    const handler = new ConfigureSessionCommandHandler(repository, navigationGateway);
    const command: ConfigureSessionCommand = {
      sessionName: 'Sprint Planning',
      estimationType: {
        id: 'fib',
        name: 'Fibonacci',
        values: [{ value: '1', label: '1', color: '', textColor: '' }, { value: '2', label: '2', color: '', textColor: '' }, { value: '3', label: '3', color: '', textColor: '' }],
      },
    };
    // Act
    await handler.execute(command);
    // Assert
    expect(repository.saved).toMatchObject({
      sessionName: 'Sprint Planning',
      step: 1,
    });
    expect(navigationGateway.navigatedTo).toBe(
      '/setup/step2?sessionName=Sprint%20Planning',
    );
  });

  it("should not configure session when name is empty", async () => {
    // Arrange
    const repository = new FakeSessionSetupRepository();
    const navigationGateway = new FakeNavigationGateway();
    const handler = new ConfigureSessionCommandHandler(repository, navigationGateway);
    const command: ConfigureSessionCommand = {
      sessionName: ' ',
      estimationType: {
        id: 'fib',
        name: 'Fibonacci',
        values: [{ value: '1', label: '1', color: '', textColor: '' }, { value: '2', label: '2', color: '', textColor: '' }, { value: '3', label: '3', color: '', textColor: '' }],
      },
    };
    // Act
    await handler.execute(command);
    // Assert
    expect(repository.saved).toBeUndefined();
    expect(navigationGateway.navigatedTo).toBeUndefined();
  });

  it("should not configure session when less than three estimation values", async () => {
    // Arrange
    const repository = new FakeSessionSetupRepository();
    const navigationGateway = new FakeNavigationGateway();
    const handler = new ConfigureSessionCommandHandler(repository, navigationGateway);
    const command: ConfigureSessionCommand = {
      sessionName: 'Sprint Planning',
      estimationType: {
        id: 'fib',
        name: 'Fibonacci',
        values: [{ value: '1', label: '1', color: '', textColor: '' }, { value: '2', label: '2', color: '', textColor: '' }],
      },
    };
    // Act
    await handler.execute(command);
    // Assert
    expect(repository.saved).toBeUndefined();
    expect(navigationGateway.navigatedTo).toBeUndefined();
  });
});
