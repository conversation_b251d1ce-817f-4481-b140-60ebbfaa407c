import { describe, it, expect } from 'vitest';
import { AddStoryCommandHandler } from '@/PokerPlanning/application/commands/addStory/addStoryCommandHandler';
import type { AddStoryCommand } from '@/PokerPlanning/application/commands/addStory/addStoryCommand';
import { StoryStore } from '@/PokerPlanning/infrastructure/stores/storyStore';

describe("When adding a story", () => {
  it("should store the new story as pending", async () => {
    // Arrange
    const storyStore = new StoryStore();
    const handler = new AddStoryCommandHandler(storyStore);
    const command: AddStoryCommand = {
      title: "New feature",
      description: "As a user, I want ...",
    };
    // Act
    await handler.execute(command);
    // Assert
    const stories = storyStore.getStories();
    expect(stories).toHaveLength(1);
    expect(stories[0]).toMatchObject({
      title: "New feature",
      description: "As a user, I want ...",
      status: "pending",
    });
  });
});
