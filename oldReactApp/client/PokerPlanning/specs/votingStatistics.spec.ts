import { describe, it, expect } from 'vitest';
import { GetVotingStatsQueryHandler } from '@/PokerPlanning/application/queries/getVotingStats/getVotingStatsQueryHandler';

interface Participant {
  id: string;
  vote: string | null;
}

describe("When computing voting statistics", () => {
  it("should return min, max, average and votes", () => {
    // Arrange
    const handler = new GetVotingStatsQueryHandler();
    const participants: Participant[] = [
      { id: '1', vote: '3' },
      { id: '2', vote: '5' },
      { id: '3', vote: '8' },
    ];
    // Act
    const stats = handler.execute({ participants });
    // Assert
    expect(stats).toEqual({
      min: 3,
      max: 8,
      avg: 5.3,
      votes: ['3', '5', '8'],
      highest: [{ id: '3', vote: '8' }],
      lowest: [{ id: '1', vote: '3' }],
    });
  });

  it("should return null when no numeric votes", () => {
    // Arrange
    const handler = new GetVotingStatsQueryHandler();
    const participants: Participant[] = [
      { id: '1', vote: 'Coffee' },
      { id: '2', vote: null },
    ];
    // Act
    const stats = handler.execute({ participants });
    // Assert
    expect(stats).toBeNull();
  });
});
