import { describe, it, expect } from 'vitest';
import { GetSessionSetupQueryHandler } from '@/PokerPlanning/application/queries/getSessionSetup/getSessionSetupQueryHandler';
import type { SessionSetupRepository, SessionSetupData } from '@/PokerPlanning/application/repositories/SessionSetupRepository';

class FakeSessionSetupRepository implements SessionSetupRepository {
  data?: SessionSetupData;
  async save(data: SessionSetupData): Promise<void> {
    this.data = data;
  }
  async get(): Promise<SessionSetupData | null> {
    return this.data ?? null;
  }
}

describe("When retrieving session setup", () => {
  it("should return stored configuration", async () => {
    // Arrange
    const repository = new FakeSessionSetupRepository();
    repository.data = { sessionName: 'Sprint', step: 1 };
    const handler = new GetSessionSetupQueryHandler(repository);
    // Act
    const result = await handler.execute();
    // Assert
    expect(result).toEqual(repository.data);
  });

  it("should return null when no setup exists", async () => {
    // Arrange
    const repository = new FakeSessionSetupRepository();
    const handler = new GetSessionSetupQueryHandler(repository);
    // Act
    const result = await handler.execute();
    // Assert
    expect(result).toBeNull();
  });
});
