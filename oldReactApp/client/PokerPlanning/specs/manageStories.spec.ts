import { describe, it, expect } from 'vitest';
import { useAddStory } from '@/PokerPlanning/infrastructure/hooks/useAddStory';
import { useEditStory } from '@/PokerPlanning/infrastructure/hooks/useEditStory';
import { useDeleteStory } from '@/PokerPlanning/infrastructure/hooks/useDeleteStory';
import { useCloseStory } from '@/PokerPlanning/infrastructure/hooks/useCloseStory';
import { storyStore } from '@/PokerPlanning/infrastructure/stores/storyStore';
import { uiStore } from '@/PokerPlanning/infrastructure/stores/uiStore';

describe("When managing stories", () => {
  it("should add a story through the hook", () => {
    // Arrange
    storyStore.setState({ stories: [], currentStoryIndex: 0 });
    const addStory = useAddStory();
    // Act
    const story = addStory("New feature", "As a user, I want ...");
    // Assert
    const stories = storyStore.getStories();
    expect(stories).toHaveLength(1);
    expect(story).toMatchObject({
      title: "New feature",
      description: "As a user, I want ...",
      status: "pending",
    });
  });

  it("should edit a story through the hook", () => {
    // Arrange
    storyStore.setState({
      stories: [
        { id: "1", title: "Old", description: "Old desc", status: "pending" },
      ],
      currentStoryIndex: 0,
    });
    const editStory = useEditStory();
    // Act
    const story = editStory("1", {
      title: "Updated",
      description: "Updated desc",
    });
    // Assert
    const stories = storyStore.getStories();
    expect(stories[0]).toMatchObject({
      id: "1",
      title: "Updated",
      description: "Updated desc",
    });
    expect(story).toMatchObject({
      id: "1",
      title: "Updated",
      description: "Updated desc",
    });
  });

  it("should delete a story through the hook", () => {
    // Arrange
    storyStore.setState({
      stories: [
        { id: "1", title: "To delete", description: "Desc", status: "pending" },
      ],
      currentStoryIndex: 0,
    });
    const deleteStory = useDeleteStory();
    // Act
    deleteStory("1");
    // Assert
    const stories = storyStore.getStories();
    expect(stories).toHaveLength(0);
  });

  it("should close the current story with a final estimate through the hook", () => {
    // Arrange
    storyStore.setState({
      stories: [
        { id: "1", title: "Story 1", description: "Desc 1", status: "active" },
        { id: "2", title: "Story 2", description: "Desc 2", status: "pending" },
      ],
      currentStoryIndex: 0,
    });
    uiStore.setState({ selectedCard: "5", votesRevealed: true, votingCardsVisible: false });
    const closeStory = useCloseStory();
    // Act
    closeStory("8");
    // Assert
    const { stories: updatedStories, currentStoryIndex } = storyStore.getState();
    expect(updatedStories[0]).toMatchObject({ status: "completed", finalEstimate: "8" });
    expect(updatedStories[1].status).toBe("active");
    expect(currentStoryIndex).toBe(1);
    expect(uiStore.getSelectedCard()).toBeNull();
    expect(uiStore.areVotesRevealed()).toBe(false);
    expect(uiStore.areVotingCardsVisible()).toBe(true);
  });
});
