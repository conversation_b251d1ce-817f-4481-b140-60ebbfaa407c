import { describe, it, expect } from 'vitest';
import { GetSessionHistoryQueryHandler } from '@/PokerPlanning/application/queries/getSessionHistory/getSessionHistoryQueryHandler';
import { RejoinSessionCommandHandler } from '@/PokerPlanning/application/commands/rejoinSession/rejoinSessionCommandHandler';
import type { AuthGateway } from '@/PokerPlanning/application/gateways/AuthGateway';
import type { SessionGateway, SessionSummary } from '@/PokerPlanning/application/gateways/SessionGateway';
import type { NavigationGateway } from '@/PokerPlanning/application/gateways/NavigationGateway';

class FakeSessionGateway implements SessionGateway {
  constructor(private sessions: SessionSummary[]) {}
  async getSessionsForUser(): Promise<SessionSummary[]> {
    return this.sessions;
  }
}

class FakeNavigationGateway implements NavigationGateway {
  navigatedTo?: string;
  navigateTo(path: string): void {
    this.navigatedTo = path;
  }
}

describe("When loading session history", () => {
  it("should separate hosted and participated sessions for the current user", async () => {
    // Arrange
    const authGateway: AuthGateway = {
      getCurrentUser: () => ({ id: '1', name: 'Jane' }),
      signOut: () => {},
    };
    const sessions: SessionSummary[] = [
      {
        id: 'A',
        name: 'Hosted',
        createdAt: '2024-01-01',
        role: 'host',
        status: 'completed',
        participantCount: 3,
        storyCount: 5,
        completedStories: 5,
      },
      {
        id: 'B',
        name: 'Joined',
        createdAt: '2024-01-02',
        role: 'participant',
        status: 'active',
        participantCount: 4,
        storyCount: 6,
        completedStories: 4,
      },
    ];
    const sessionGateway = new FakeSessionGateway(sessions);
    const handler = new GetSessionHistoryQueryHandler(authGateway, sessionGateway);
    // Act
    const result = await handler.execute();
    // Assert
    expect(result.hosted).toHaveLength(1);
    expect(result.participated).toHaveLength(1);
  });
});

describe("When rejoining a session", () => {
  it("should navigate to the planning room when session is active", () => {
    // Arrange
    const navigationGateway = new FakeNavigationGateway();
    const handler = new RejoinSessionCommandHandler(navigationGateway);
    // Act
    handler.execute({ sessionId: 'ABC', status: 'active' });
    // Assert
    expect(navigationGateway.navigatedTo).toBe('/room/ABC');
  });

  it("should not navigate when session is completed", () => {
    // Arrange
    const navigationGateway = new FakeNavigationGateway();
    const handler = new RejoinSessionCommandHandler(navigationGateway);
    // Act
    handler.execute({ sessionId: 'ABC', status: 'completed' });
    // Assert
    expect(navigationGateway.navigatedTo).toBeUndefined();
  });
});

