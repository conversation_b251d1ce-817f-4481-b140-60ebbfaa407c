import { describe, it, expect } from 'vitest';
import { SelectCardCommandHandler } from '@/PokerPlanning/application/commands/selectCard/selectCardCommandHandler';
import { UiStore } from '@/PokerPlanning/infrastructure/stores/uiStore';

describe("When selecting a card", () => {
  it("should save the selected card and hide voting cards", async () => {
    // Arrange
    const uiStore = new UiStore();
    const handler = new SelectCardCommandHandler(uiStore);
    // Act
    await handler.execute({ cardValue: "5" });
    // Assert
    expect(uiStore.getSelectedCard()).toBe("5");
    expect(uiStore.areVotingCardsVisible()).toBe(false);
  });
});
