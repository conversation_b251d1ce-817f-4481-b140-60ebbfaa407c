import { describe, it, expect } from 'vitest';
import { EditStoryCommandHandler } from '@/PokerPlanning/application/commands/editStory/editStoryCommandHandler';
import type { EditStoryCommand } from '@/PokerPlanning/application/commands/editStory/editStoryCommand';
import { StoryStore } from '@/PokerPlanning/infrastructure/stores/storyStore';
import type { Story } from '@/PokerPlanning/application/stores/storyStore';

describe("When editing a story", () => {
  it("should update the story with new values", async () => {
    // Arrange
    const initialStories: Story[] = [
      { id: "1", title: "Old title", description: "Old desc", status: "pending" },
      { id: "2", title: "Second", description: "Second desc", status: "pending" },
    ];
    const storyStore = new StoryStore(initialStories);
    const handler = new EditStoryCommandHandler(storyStore);
    const command: EditStoryCommand = {
      id: "1",
      title: "Updated title",
      description: "Updated desc",
    };
    // Act
    await handler.execute(command);
    // Assert
    const stories = storyStore.getStories();
    expect(stories).toHaveLength(2);
    expect(stories[0]).toMatchObject({
      id: "1",
      title: "Updated title",
      description: "Updated desc",
    });
    expect(stories[1]).toMatchObject({
      id: "2",
      title: "Second",
      description: "Second desc",
    });
  });
});
