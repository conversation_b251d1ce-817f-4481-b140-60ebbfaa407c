import { describe, it, expect } from 'vitest';
import { StoryStore } from '@/PokerPlanning/infrastructure/stores/storyStore';
import { AddSetupStoryCommandHandler } from '@/PokerPlanning/application/commands/addSetupStory/addSetupStoryCommandHandler';
import { EditSetupStoryCommandHandler } from '@/PokerPlanning/application/commands/editSetupStory/editSetupStoryCommandHandler';
import { DeleteSetupStoryCommandHandler } from '@/PokerPlanning/application/commands/deleteSetupStory/deleteSetupStoryCommandHandler';
import type { SessionSetupRepository, SessionSetupData } from '@/PokerPlanning/application/repositories/SessionSetupRepository';

class FakeSessionSetupRepository implements SessionSetupRepository {
  saved?: SessionSetupData;
  async save(data: SessionSetupData): Promise<void> {
    this.saved = data;
  }
  async get(): Promise<SessionSetupData | null> {
    return this.saved ?? null;
  }
}

describe("When managing setup stories", () => {
  it("should add a story and persist it", async () => {
    // Arrange
    const repository = new FakeSessionSetupRepository();
    const store = new StoryStore();
    const handler = new AddSetupStoryCommandHandler(store, repository);
    // Act
    await handler.execute({ title: 'Story 1', description: 'Desc 1' });
    // Assert
    const stories = store.getStories();
    expect(stories).toHaveLength(1);
    expect(repository.saved).toMatchObject({ stories, step: 2 });
  });

  it("should edit a story and persist changes", async () => {
    // Arrange
    const repository = new FakeSessionSetupRepository();
    const store = new StoryStore([
      { id: '1', title: 'Old', description: 'Old', status: 'pending' },
    ]);
    const handler = new EditSetupStoryCommandHandler(store, repository);
    // Act
    await handler.execute({ storyId: '1', title: 'Updated', description: 'New' });
    // Assert
    const stories = store.getStories();
    expect(stories[0]).toMatchObject({ id: '1', title: 'Updated', description: 'New' });
    expect(repository.saved).toMatchObject({ stories, step: 2 });
  });

  it("should delete a story and persist changes", async () => {
    // Arrange
    const repository = new FakeSessionSetupRepository();
    const store = new StoryStore([
      { id: '1', title: 'To delete', description: 'Desc', status: 'pending' },
    ]);
    const handler = new DeleteSetupStoryCommandHandler(store, repository);
    // Act
    await handler.execute({ storyId: '1' });
    // Assert
    const stories = store.getStories();
    expect(stories).toHaveLength(0);
    expect(repository.saved).toMatchObject({ stories, step: 2 });
  });
});
