import { describe, it, expect } from 'vitest';
import { StartSessionCommandHandler } from '@/PokerPlanning/application/commands/startSession/startSessionCommandHandler';
import type { StartSessionCommand } from '@/PokerPlanning/application/commands/startSession/startSessionCommand';
import type { NavigationGateway } from '@/PokerPlanning/application/gateways/NavigationGateway';
import type { SessionRepository, SessionData } from '@/PokerPlanning/application/repositories/SessionRepository';

class FakeSessionRepository implements SessionRepository {
  saved?: SessionData;
  async save(session: SessionData): Promise<string> {
    this.saved = session;
    return 'ABC123';
  }
  async updateEstimationType(): Promise<void> {}
}

class FakeNavigationGateway implements NavigationGateway {
  navigatedTo?: string;
  navigateTo(path: string): void {
    this.navigatedTo = path;
  }
}

describe("When starting a session", () => {
  it("should persist configuration and navigate host to planning room", async () => {
    // Arrange
    const sessionRepository = new FakeSessionRepository();
    const navigationGateway = new FakeNavigationGateway();
    const handler = new StartSessionCommandHandler(
      sessionRepository,
      navigationGateway,
    );
    const command: StartSessionCommand = {
      sessionName: 'Sprint Planning',
      estimationType: { id: 'fib', name: 'Fibonacci', values: [] },
      stories: [{ id: '1', title: 'Story 1', description: 'Desc' }],
      hostName: 'Jane',
    };
    // Act
    await handler.execute(command);
    // Assert
    expect(sessionRepository.saved).toMatchObject({
      sessionName: 'Sprint Planning',
      createdBy: 'Jane',
    });
    expect(navigationGateway.navigatedTo).toBe(
      '/room/ABC123?name=Jane&sessionName=Sprint%20Planning&host=true',
    );
  });

  it("should not start session when session name is empty", async () => {
    // Arrange
    const sessionRepository = new FakeSessionRepository();
    const navigationGateway = new FakeNavigationGateway();
    const handler = new StartSessionCommandHandler(
      sessionRepository,
      navigationGateway,
    );
    const command: StartSessionCommand = {
      sessionName: ' ',
      estimationType: { id: 'fib', name: 'Fibonacci', values: [] },
      stories: [{ id: '1', title: 'Story 1', description: 'Desc' }],
      hostName: 'Jane',
    };
    // Act
    await handler.execute(command);
    // Assert
    expect(sessionRepository.saved).toBeUndefined();
    expect(navigationGateway.navigatedTo).toBeUndefined();
  });

  it("should not start session when no stories provided", async () => {
    // Arrange
    const sessionRepository = new FakeSessionRepository();
    const navigationGateway = new FakeNavigationGateway();
    const handler = new StartSessionCommandHandler(
      sessionRepository,
      navigationGateway,
    );
    const command: StartSessionCommand = {
      sessionName: 'Sprint Planning',
      estimationType: { id: 'fib', name: 'Fibonacci', values: [] },
      stories: [],
      hostName: 'Jane',
    };
    // Act
    await handler.execute(command);
    // Assert
    expect(sessionRepository.saved).toBeUndefined();
    expect(navigationGateway.navigatedTo).toBeUndefined();
  });
});
