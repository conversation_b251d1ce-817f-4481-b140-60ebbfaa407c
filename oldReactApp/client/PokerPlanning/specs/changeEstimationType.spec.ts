import { describe, it, expect } from 'vitest';
import { ChangeEstimationTypeCommandHandler } from '@/PokerPlanning/application/commands/changeEstimationType/changeEstimationTypeCommandHandler';
import type { EstimationType } from '@/PokerPlanning/application/commands/startSession/startSessionCommand';
import type { SessionRepository } from '@/PokerPlanning/application/repositories/SessionRepository';
import { UiStore } from '@/PokerPlanning/infrastructure/stores/uiStore';

describe("When changing the estimation type", () => {
  it("should persist the new type and reset voting state", async () => {
    // Arrange
    class FakeSessionRepository implements SessionRepository {
      updated?: { sessionId: string; estimationType: EstimationType };
      async save() { return ''; }
      async updateEstimationType(sessionId: string, estimationType: EstimationType): Promise<void> {
        this.updated = { sessionId, estimationType };
      }
    }
    const sessionRepository = new FakeSessionRepository();
    const uiStore = new UiStore();
    uiStore.setState({ selectedCard: '5', votesRevealed: true, votingCardsVisible: false });
    const handler = new ChangeEstimationTypeCommandHandler(sessionRepository, uiStore);
    const estimationType: EstimationType = { id: 'tshirt', name: 'T-Shirt', values: [] };
    // Act
    await handler.execute({ sessionId: 'ABC123', estimationType });
    // Assert
    expect(sessionRepository.updated).toEqual({ sessionId: 'ABC123', estimationType });
    expect(uiStore.getSelectedCard()).toBeNull();
    expect(uiStore.areVotesRevealed()).toBe(false);
    expect(uiStore.areVotingCardsVisible()).toBe(true);
  });

  it("should not change estimation type when session id is empty", async () => {
    // Arrange
    class FakeSessionRepository implements SessionRepository {
      updated?: { sessionId: string; estimationType: EstimationType };
      async save() { return ''; }
      async updateEstimationType(sessionId: string, estimationType: EstimationType): Promise<void> {
        this.updated = { sessionId, estimationType };
      }
    }
    const sessionRepository = new FakeSessionRepository();
    const uiStore = new UiStore();
    uiStore.setState({ selectedCard: '5', votesRevealed: true, votingCardsVisible: false });
    const handler = new ChangeEstimationTypeCommandHandler(sessionRepository, uiStore);
    const estimationType: EstimationType = { id: 'fib', name: 'Fibonacci', values: [] };
    // Act
    await handler.execute({ sessionId: ' ', estimationType });
    // Assert
    expect(sessionRepository.updated).toBeUndefined();
    expect(uiStore.getSelectedCard()).toBe('5');
    expect(uiStore.areVotesRevealed()).toBe(true);
    expect(uiStore.areVotingCardsVisible()).toBe(false);
  });
});
