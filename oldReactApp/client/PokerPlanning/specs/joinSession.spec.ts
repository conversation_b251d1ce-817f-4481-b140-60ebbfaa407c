import { describe, it, expect } from 'vitest';
import { JoinSessionCommandHandler } from '@/PokerPlanning/application/commands/joinSession/joinSessionCommandHandler';
import type { NavigationGateway } from '@/PokerPlanning/application/gateways/NavigationGateway';

class FakeNavigationGateway implements NavigationGateway {
  navigatedTo?: string;
  navigateTo(path: string): void {
    this.navigatedTo = path;
  }
}

describe("When joining a session", () => {
  it("should navigate to the planning room when inputs are valid", async () => {
    // Arrange
    const navigationGateway = new FakeNavigationGateway();
    const handler = new JoinSessionCommandHandler(navigationGateway);
    // Act
    await handler.execute({ sessionCode: "ABC123", participantName: "John" });
    // Assert
    expect(navigationGateway.navigatedTo).toBe("/room/ABC123?name=John");
  });

  it("should not navigate when session code is empty", async () => {
    // Arrange
    const navigationGateway = new FakeNavigationGateway();
    const handler = new JoinSessionCommandHandler(navigationGateway);
    // Act
    await handler.execute({ sessionCode: "", participantName: "John" });
    // Assert
    expect(navigationGateway.navigatedTo).toBeUndefined();
  });

  it("should not navigate when participant name is empty", async () => {
    // Arrange
    const navigationGateway = new FakeNavigationGateway();
    const handler = new JoinSessionCommandHandler(navigationGateway);
    // Act
    await handler.execute({ sessionCode: "ABC123", participantName: "" });
    // Assert
    expect(navigationGateway.navigatedTo).toBeUndefined();
  });
});
