import { describe, it, expect, vi } from 'vitest';
import { useCopySessionCode } from '@/PokerPlanning/infrastructure/hooks/useCopySessionCode';

describe("When copying the session code", () => {
  it("should copy the session code to the clipboard", async () => {
    // Arrange
    const writeText = vi.fn().mockResolvedValue(undefined);
    (globalThis as any).navigator = { clipboard: { writeText } };
    const copySessionCode = useCopySessionCode();
    // Act
    await copySessionCode("ABC123");
    // Assert
    expect(writeText).toHaveBeenCalledWith("ABC123");
  });
});
