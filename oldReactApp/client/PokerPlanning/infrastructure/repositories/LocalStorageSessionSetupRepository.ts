import type {
  SessionSetupRepository,
  SessionSetupData,
} from '@/PokerPlanning/application/repositories/SessionSetupRepository';

export class LocalStorageSessionSetupRepository implements SessionSetupRepository {
  async save(data: SessionSetupData): Promise<void> {
    const existing = localStorage.getItem('session_setup');
    const parsed = existing ? JSON.parse(existing) : {};
    const updated = { ...parsed, ...data };
    localStorage.setItem('session_setup', JSON.stringify(updated));
  }

  async get(): Promise<SessionSetupData | null> {
    const existing = localStorage.getItem('session_setup');
    return existing ? JSON.parse(existing) : null;
  }
}
