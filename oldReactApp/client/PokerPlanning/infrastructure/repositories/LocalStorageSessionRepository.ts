import type {
  SessionData,
  SessionRepository,
} from '@/PokerPlanning/application/repositories/SessionRepository';
import type { EstimationType } from '@/PokerPlanning/application/commands/startSession/startSessionCommand';

export class LocalStorageSessionRepository implements SessionRepository {
  async save(session: SessionData): Promise<string> {
    const sessionId = Math.random().toString(36).substring(2, 8).toUpperCase();
    localStorage.setItem(
      `session_${sessionId}`,
      JSON.stringify({
        sessionName: session.sessionName,
        estimationType: session.estimationType,
        stories: session.stories,
        createdBy: session.createdBy,
      }),
    );
    return sessionId;
  }

  async updateEstimationType(
    sessionId: string,
    estimationType: EstimationType,
  ): Promise<void> {
    const sessionData = localStorage.getItem(`session_${sessionId}`);
    if (!sessionData) return;
    const parsed = JSON.parse(sessionData);
    parsed.estimationType = estimationType;
    localStorage.setItem(`session_${sessionId}`, JSON.stringify(parsed));
  }
}
