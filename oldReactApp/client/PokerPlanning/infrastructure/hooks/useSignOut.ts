import { useAuth } from '@/contexts/AuthContext';
import { SignOutCommandHandler } from '@/PokerPlanning/application/commands/signOut/signOutCommandHandler';
import { AuthContextAuthGateway } from '@/PokerPlanning/infrastructure/gateways/AuthContextAuthGateway';

export function useSignOut() {
  const { user, logout } = useAuth();
  const authGateway = new AuthContextAuthGateway(user, logout);
  const handler = new SignOutCommandHandler(authGateway);
  return () => handler.execute();
}
