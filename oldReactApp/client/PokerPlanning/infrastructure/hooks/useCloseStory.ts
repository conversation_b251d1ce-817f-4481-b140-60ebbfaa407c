import { CloseStoryCommandHandler } from '@/PokerPlanning/application/commands/closeStory/closeStoryCommandHandler';
import { storyStore } from '@/PokerPlanning/infrastructure/stores/storyStore';
import { uiStore } from '@/PokerPlanning/infrastructure/stores/uiStore';

export function useCloseStory() {
  const handler = new CloseStoryCommandHandler(storyStore, uiStore);
  return (finalEstimate: string): void => {
    handler.execute({ finalEstimate });
  };
}
