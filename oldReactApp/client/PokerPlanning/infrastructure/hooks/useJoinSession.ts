import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { JoinSessionCommandHandler } from '@/PokerPlanning/application/commands/joinSession/joinSessionCommandHandler';
import { RouterNavigationGateway } from '@/PokerPlanning/infrastructure/gateways/RouterNavigationGateway';

export function useJoinSession() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const navigationGateway = new RouterNavigationGateway(navigate);
  const handler = new JoinSessionCommandHandler(navigationGateway);
  return (sessionCode: string, participantName?: string) =>
    handler.execute({
      sessionCode,
      participantName: user ? user.name : participantName ?? '',
    });
}
