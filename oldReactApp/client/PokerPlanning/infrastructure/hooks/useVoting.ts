import { useSyncExternalStore } from 'react';
import { uiStore } from '@/PokerPlanning/infrastructure/stores/uiStore';
import { SelectCardCommandHandler } from '@/PokerPlanning/application/commands/selectCard/selectCardCommandHandler';
import { RevealVotesCommandHandler } from '@/PokerPlanning/application/commands/revealVotes/revealVotesCommandHandler';
import { ResetVotesCommandHandler } from '@/PokerPlanning/application/commands/resetVotes/resetVotesCommandHandler';
import { ChangeVoteCommandHandler } from '@/PokerPlanning/application/commands/changeVote/changeVoteCommandHandler';

export function useVoting() {
  const selectCardHandler = new SelectCardCommandHandler(uiStore);
  const revealVotesHandler = new RevealVotesCommandHandler(uiStore);
  const resetVotesHandler = new ResetVotesCommandHandler(uiStore);
  const changeVoteHandler = new ChangeVoteCommandHandler(uiStore);
  const subscribe = (listener: () => void) =>
    uiStore.subscribe(() => listener());

  const selectedCard = useSyncExternalStore(
    subscribe,
    () => uiStore.getSelectedCard(),
  );
  const votesRevealed = useSyncExternalStore(
    subscribe,
    () => uiStore.areVotesRevealed(),
  );
  const votingCardsVisible = useSyncExternalStore(
    subscribe,
    () => uiStore.areVotingCardsVisible(),
  );

  return {
    selectCard: (cardValue: string) => selectCardHandler.execute({ cardValue }),
    revealVotes: () => revealVotesHandler.execute(),
    resetVotes: () => resetVotesHandler.execute(),
    changeVote: () => changeVoteHandler.execute(),
    selectedCard,
    votesRevealed,
    votingCardsVisible,
  };
}
