import { EditSetupStoryCommandHandler } from '@/PokerPlanning/application/commands/editSetupStory/editSetupStoryCommandHandler';
import { storyStore } from '@/PokerPlanning/infrastructure/stores/storyStore';
import { LocalStorageSessionSetupRepository } from '@/PokerPlanning/infrastructure/repositories/LocalStorageSessionSetupRepository';

export function useEditSetupStory() {
  const repository = new LocalStorageSessionSetupRepository();
  const handler = new EditSetupStoryCommandHandler(storyStore, repository);
  return (storyId: string, title: string, description: string) =>
    handler.execute({ storyId, title, description });
}
