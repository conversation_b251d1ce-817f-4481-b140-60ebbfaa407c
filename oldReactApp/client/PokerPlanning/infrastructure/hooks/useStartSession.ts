import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { StartSessionCommandHandler } from '@/PokerPlanning/application/commands/startSession/startSessionCommandHandler';
import type {
  EstimationType,
  Story,
} from '@/PokerPlanning/application/commands/startSession/startSessionCommand';
import { RouterNavigationGateway } from '@/PokerPlanning/infrastructure/gateways/RouterNavigationGateway';
import { LocalStorageSessionRepository } from '@/PokerPlanning/infrastructure/repositories/LocalStorageSessionRepository';

export function useStartSession() {
  const navigate = useNavigate();
  const { user } = useAuth();
  const sessionRepository = new LocalStorageSessionRepository();
  const navigationGateway = new RouterNavigationGateway(navigate);
  const handler = new StartSessionCommandHandler(
    sessionRepository,
    navigationGateway,
  );
  return (
    sessionName: string,
    estimationType: EstimationType,
    stories: Story[],
  ) =>
    handler.execute({
      sessionName,
      estimationType,
      stories,
      hostName: user?.name ?? '',
    });
}
