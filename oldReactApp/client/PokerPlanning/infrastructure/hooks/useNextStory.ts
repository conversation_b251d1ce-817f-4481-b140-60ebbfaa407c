import { NextStoryCommandHandler } from '@/PokerPlanning/application/commands/nextStory/nextStoryCommandHandler';
import { storyStore } from '@/PokerPlanning/infrastructure/stores/storyStore';
import { uiStore } from '@/PokerPlanning/infrastructure/stores/uiStore';

export function useNextStory() {
  const handler = new NextStoryCommandHandler(storyStore, uiStore);
  return (): void => {
    handler.execute();
  };
}
