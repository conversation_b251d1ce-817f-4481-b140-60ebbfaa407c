import { EditStoryCommandHandler } from '@/PokerPlanning/application/commands/editStory/editStoryCommandHandler';
import { storyStore } from '@/PokerPlanning/infrastructure/stores/storyStore';
import type { Story } from '@/PokerPlanning/application/stores/storyStore';

export function useEditStory() {
  const handler = new EditStoryCommandHandler(storyStore);
  return (
    id: string,
    { title, description }: { title: string; description: string },
  ): Story => {
    void handler.execute({ id, title, description });
    const stories = storyStore.getStories();
    return stories.find((story) => story.id === id)!;
  };
}
