import { useEffect, useState } from 'react';
import type { SessionSetupData } from '@/PokerPlanning/application/repositories/SessionSetupRepository';
import { GetSessionSetupQueryHandler } from '@/PokerPlanning/application/queries/getSessionSetup/getSessionSetupQueryHandler';
import { LocalStorageSessionSetupRepository } from '@/PokerPlanning/infrastructure/repositories/LocalStorageSessionSetupRepository';

export function useSessionSetup() {
  const [data, setData] = useState<SessionSetupData | null | undefined>(
    undefined,
  );
  useEffect(() => {
    const repository = new LocalStorageSessionSetupRepository();
    const handler = new GetSessionSetupQueryHandler(repository);
    void handler.execute().then(setData);
  }, []);
  return data;
}
