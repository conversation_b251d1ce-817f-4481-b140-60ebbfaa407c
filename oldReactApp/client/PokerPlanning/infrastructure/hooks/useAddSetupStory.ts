import { AddSetupStoryCommandHandler } from '@/PokerPlanning/application/commands/addSetupStory/addSetupStoryCommandHandler';
import { storyStore } from '@/PokerPlanning/infrastructure/stores/storyStore';
import { LocalStorageSessionSetupRepository } from '@/PokerPlanning/infrastructure/repositories/LocalStorageSessionSetupRepository';

export function useAddSetupStory() {
  const repository = new LocalStorageSessionSetupRepository();
  const handler = new AddSetupStoryCommandHandler(storyStore, repository);
  return (title: string, description: string) =>
    handler.execute({ title, description });
}
