import { useNavigate } from 'react-router-dom';
import { EndSessionCommandHandler } from '@/PokerPlanning/application/commands/endSession/endSessionCommandHandler';
import { RouterNavigationGateway } from '@/PokerPlanning/infrastructure/gateways/RouterNavigationGateway';

export function useEndSession() {
  const navigate = useNavigate();
  const navigationGateway = new RouterNavigationGateway(navigate);
  const handler = new EndSessionCommandHandler(navigationGateway);
  return () => handler.execute();
}
