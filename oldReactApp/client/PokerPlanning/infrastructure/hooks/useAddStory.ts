import { AddStoryCommandHandler } from '@/PokerPlanning/application/commands/addStory/addStoryCommandHandler';
import { storyStore } from '@/PokerPlanning/infrastructure/stores/storyStore';
import type { Story } from '@/PokerPlanning/application/stores/storyStore';

export function useAddStory() {
  const handler = new AddStoryCommandHandler(storyStore);
  return (title: string, description: string): Story => {
    void handler.execute({ title, description });
    const stories = storyStore.getStories();
    return stories[stories.length - 1];
  };
}
