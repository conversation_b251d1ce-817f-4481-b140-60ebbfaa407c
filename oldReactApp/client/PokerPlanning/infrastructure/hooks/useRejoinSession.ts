import { useNavigate } from 'react-router-dom';
import { RejoinSessionCommandHandler } from '@/PokerPlanning/application/commands/rejoinSession/rejoinSessionCommandHandler';
import { RouterNavigationGateway } from '@/PokerPlanning/infrastructure/gateways/RouterNavigationGateway';

export function useRejoinSession() {
  const navigate = useNavigate();
  const navigationGateway = new RouterNavigationGateway(navigate);
  const handler = new RejoinSessionCommandHandler(navigationGateway);
  return (sessionId: string, status: 'completed' | 'active') =>
    handler.execute({ sessionId, status });
}
