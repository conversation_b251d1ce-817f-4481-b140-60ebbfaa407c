import { useNavigate } from 'react-router-dom';
import { ConfigureSessionCommandHandler } from '@/PokerPlanning/application/commands/configureSession/configureSessionCommandHandler';
import type { EstimationType } from '@/PokerPlanning/application/commands/startSession/startSessionCommand';
import { RouterNavigationGateway } from '@/PokerPlanning/infrastructure/gateways/RouterNavigationGateway';
import { LocalStorageSessionSetupRepository } from '@/PokerPlanning/infrastructure/repositories/LocalStorageSessionSetupRepository';

export function useConfigureSession() {
  const navigate = useNavigate();
  const repository = new LocalStorageSessionSetupRepository();
  const navigationGateway = new RouterNavigationGateway(navigate);
  const handler = new ConfigureSessionCommandHandler(
    repository,
    navigationGateway,
  );
  return (sessionName: string, estimationType: EstimationType) =>
    handler.execute({ sessionName, estimationType });
}
