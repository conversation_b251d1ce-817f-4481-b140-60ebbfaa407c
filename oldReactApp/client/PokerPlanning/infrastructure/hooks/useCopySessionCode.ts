import { NavigatorClipboardGateway } from '@/PokerPlanning/infrastructure/gateways/NavigatorClipboardGateway';
import { CopySessionCodeCommandHandler } from '@/PokerPlanning/application/commands/copySessionCode/copySessionCodeCommandHandler';

export function useCopySessionCode() {
  const clipboardGateway = new NavigatorClipboardGateway();
  const handler = new CopySessionCodeCommandHandler(clipboardGateway);
  return (sessionCode: string) => handler.execute({ sessionCode });
}
