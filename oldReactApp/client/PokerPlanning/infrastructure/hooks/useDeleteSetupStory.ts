import { DeleteSetupStoryCommandHandler } from '@/PokerPlanning/application/commands/deleteSetupStory/deleteSetupStoryCommandHandler';
import { storyStore } from '@/PokerPlanning/infrastructure/stores/storyStore';
import { LocalStorageSessionSetupRepository } from '@/PokerPlanning/infrastructure/repositories/LocalStorageSessionSetupRepository';

export function useDeleteSetupStory() {
  const repository = new LocalStorageSessionSetupRepository();
  const handler = new DeleteSetupStoryCommandHandler(storyStore, repository);
  return (storyId: string) => handler.execute({ storyId });
}
