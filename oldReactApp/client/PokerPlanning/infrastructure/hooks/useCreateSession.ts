import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { CreateSessionCommandHandler } from '@/PokerPlanning/application/commands/createSession/createSessionCommandHandler';
import { AuthContextAuthGateway } from '@/PokerPlanning/infrastructure/gateways/AuthContextAuthGateway';
import { RouterNavigationGateway } from '@/PokerPlanning/infrastructure/gateways/RouterNavigationGateway';

export function useCreateSession() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const authGateway = new AuthContextAuthGateway(user);
  const navigationGateway = new RouterNavigationGateway(navigate);
  const handler = new CreateSessionCommandHandler(authGateway, navigationGateway);
  return () => handler.execute();
}
