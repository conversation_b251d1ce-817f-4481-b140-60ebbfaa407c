import { ChangeEstimationTypeCommandHandler } from '@/PokerPlanning/application/commands/changeEstimationType/changeEstimationTypeCommandHandler';
import type { EstimationType } from '@/PokerPlanning/application/commands/startSession/startSessionCommand';
import { LocalStorageSessionRepository } from '@/PokerPlanning/infrastructure/repositories/LocalStorageSessionRepository';
import { uiStore } from '@/PokerPlanning/infrastructure/stores/uiStore';

export function useChangeEstimationType() {
  const sessionRepository = new LocalStorageSessionRepository();
  const handler = new ChangeEstimationTypeCommandHandler(
    sessionRepository,
    uiStore,
  );
  return (sessionId: string, estimationType: EstimationType) =>
    handler.execute({ sessionId, estimationType });
}
