import { createStore, type Store<PERSON><PERSON> } from 'zustand/vanilla';
import type { UiState, UiStore as UiStoreContract } from '@/PokerPlanning/application/stores/uiStore';

export class UiStore implements UiStoreContract {
  private readonly store: StoreApi<UiState>;
  getState: StoreApi<UiState>["getState"];
  getInitialState: StoreApi<UiState>["getInitialState"];
  setState: StoreApi<UiState>["setState"];
  subscribe: StoreApi<UiState>["subscribe"];

  constructor() {
    this.store = createStore<UiState>(() => ({
      selectedCard: null,
      votesRevealed: false,
      votingCardsVisible: true,
    }));
    this.getState = this.store.getState;
    this.getInitialState = this.store.getInitialState;
    this.setState = this.store.setState;
    this.subscribe = this.store.subscribe;
  }

  selectCard(card: string): void {
    this.store.setState({ selectedCard: card });
    console.log('card selected', card);
  }

  deselectCard(): void {
    this.store.setState({ selectedCard: null });
    console.log('card deselected');
  }

  toggleVotesRevealed(): void {
    this.store.setState((state) => ({ votesRevealed: !state.votesRevealed }));
    console.log('votes revealed toggled');
  }

  hideAllVotes(): void {
    this.store.setState({ votesRevealed: false });
    console.log('all votes hidden');
  }

  showVotingCards(): void {
    this.store.setState({ votingCardsVisible: true });
    console.log('voting cards shown');
  }

  hideVotingCards(): void {
    this.store.setState({ votingCardsVisible: false });
    console.log('voting cards hidden');
  }

  getSelectedCard(): string | null {
    console.log('getSelectedCard', this.store.getState().selectedCard);
    return this.store.getState().selectedCard;
  }

  areVotesRevealed(): boolean {
    console.log('areVotesRevealed', this.store.getState().votesRevealed);
    return this.store.getState().votesRevealed;
  }

  areVotingCardsVisible(): boolean {
    console.log('areVotingCardsVisible', this.store.getState().votingCardsVisible);
    return this.store.getState().votingCardsVisible;
  }

}

export const uiStore = new UiStore();
