import { createStore, type Store<PERSON><PERSON> } from 'zustand/vanilla';
import type {
  Story,
  StoryState,
  StoryStore as StoryStoreContract,
} from '@/PokerPlanning/application/stores/storyStore';

export class StoryStore implements StoryStoreContract {
  private readonly store: StoreApi<StoryState>;
  getState: StoreApi<StoryState>["getState"];
  getInitialState: StoreApi<StoryState>["getInitialState"];
  setState: StoreApi<StoryState>["setState"];
  subscribe: StoreApi<StoryState>["subscribe"];

  constructor(initialStories: Story[] = []) {
    this.store = createStore<StoryState>(() => ({
      stories: initialStories,
      currentStoryIndex: 0,
    }));
    this.getState = this.store.getState;
    this.getInitialState = this.store.getInitialState;
    this.setState = this.store.setState;
    this.subscribe = this.store.subscribe;
  }

  addStory(story: Story): void {
    this.store.setState((state) => ({ stories: [...state.stories, story] }));
    console.log('story added', story);
  }

  updateStory(
    storyId: string,
    updates: { title: string; description: string },
  ): void {
    this.store.setState((state) => ({
      stories: state.stories.map((story) =>
        story.id === storyId ? { ...story, ...updates } : story,
      ),
    }));
    console.log('story updated', storyId, updates);
  }

  deleteStory(storyId: string): void {
    this.store.setState((state) => ({
      stories: state.stories.filter((story) => story.id !== storyId),
    }));
    console.log('story deleted', storyId);
  }

  moveToNextStory(): boolean {
    let moved = false;
    this.store.setState((state) => {
      if (state.currentStoryIndex >= state.stories.length - 1) {
        return state;
      }
      moved = true;
      const nextIndex = state.currentStoryIndex + 1;
      return {
        stories: state.stories.map((story, index) =>
          index === state.currentStoryIndex && story.status === 'active'
            ? { ...story, status: 'completed' }
            : index === nextIndex
            ? { ...story, status: 'active' }
            : story,
        ),
        currentStoryIndex: nextIndex,
      };
    });
    console.log('moved to next story', moved);
    return moved;
  }

  closeCurrentStory(finalEstimate: string): void {
    this.store.setState((state) => {
      const updatedStories = state.stories.map((story, index): Story =>
        index === state.currentStoryIndex
          ? { ...story, status: 'completed' as const, finalEstimate }
          : story,
      );
      let nextIndex = state.currentStoryIndex;
      if (state.currentStoryIndex < state.stories.length - 1) {
        nextIndex = state.currentStoryIndex + 1;
        updatedStories[nextIndex] = {
          ...updatedStories[nextIndex],
          status: 'active' as const,
        };
      }
      return {
        stories: updatedStories,
        currentStoryIndex: nextIndex,
      };
    });
    console.log('story closed', finalEstimate);
  }

  getStories(): Story[] {
    console.log('getStories', this.store.getState().stories);
    return this.store.getState().stories;
  }

}

export const storyStore = new StoryStore();
