import { describe, it, expect } from 'vitest';
import { SignInCommandHandler } from '@/Authentication/application/commands/signIn/signInCommandHandler';
import type { AuthGateway } from '@/Authentication/application/gateways/AuthGateway';
import type { NavigationGateway } from '@/Authentication/application/gateways/NavigationGateway';

class FakeAuthGateway implements AuthGateway {
  constructor(private readonly shouldSucceed: boolean) {}
  login(): Promise<boolean> {
    return Promise.resolve(this.shouldSucceed);
  }
  register(): Promise<boolean> {
    return Promise.resolve(true);
  }
}

class FakeNavigationGateway implements NavigationGateway {
  navigatedTo?: string;
  navigateTo(path: string): void {
    this.navigatedTo = path;
  }
}

describe("When signing in", () => {
  it("should redirect to the requested page on success", async () => {
    // Arrange
    const authGateway = new FakeAuthGateway(true);
    const navigationGateway = new FakeNavigationGateway();
    const handler = new SignInCommandHandler(authGateway, navigationGateway);
    // Act
    const result = await handler.execute({ email: "<EMAIL>", password: "password123", redirectTo: "/dashboard" });
    // Assert
    expect(result).toBe(true);
    expect(navigationGateway.navigatedTo).toBe("/dashboard");
  });

  it("should not redirect when credentials are invalid", async () => {
    // Arrange
    const authGateway = new FakeAuthGateway(false);
    const navigationGateway = new FakeNavigationGateway();
    const handler = new SignInCommandHandler(authGateway, navigationGateway);
    // Act
    const result = await handler.execute({ email: "<EMAIL>", password: "wrong", redirectTo: "/dashboard" });
    // Assert
    expect(result).toBe(false);
    expect(navigationGateway.navigatedTo).toBeUndefined();
  });
});
