import { describe, it, expect } from 'vitest';
import { RegisterCommandHandler } from '@/Authentication/application/commands/register/registerCommandHandler';
import type { AuthGateway } from '@/Authentication/application/gateways/AuthGateway';
import type { NavigationGateway } from '@/Authentication/application/gateways/NavigationGateway';

class FakeAuthGateway implements AuthGateway {
  constructor(private readonly shouldSucceed: boolean) {}
  login(): Promise<boolean> {
    return Promise.resolve(true);
  }
  register(): Promise<boolean> {
    return Promise.resolve(this.shouldSucceed);
  }
}

class FakeNavigationGateway implements NavigationGateway {
  navigatedTo?: string;
  navigateTo(path: string): void {
    this.navigatedTo = path;
  }
}

describe("When registering", () => {
  it("should navigate to the requested page on success", async () => {
    // Arrange
    const authGateway = new FakeAuthGateway(true);
    const navigationGateway = new FakeNavigationGateway();
    const handler = new RegisterCommandHandler(authGateway, navigationGateway);
    // Act
    const result = await handler.execute({
      name: "<PERSON>", email: "<EMAIL>", password: "password123", confirmPassword: "password123", redirectTo: "/dashboard" });
    // Assert
    expect(result).toEqual({ success: true });
    expect(navigationGateway.navigatedTo).toBe("/dashboard");
  });

  it("should fail when passwords do not match", async () => {
    // Arrange
    const authGateway = new FakeAuthGateway(true);
    const navigationGateway = new FakeNavigationGateway();
    const handler = new RegisterCommandHandler(authGateway, navigationGateway);
    // Act
    const result = await handler.execute({
      name: "Alex", email: "<EMAIL>", password: "pass1", confirmPassword: "pass2", redirectTo: "/dashboard" });
    // Assert
    expect(result).toEqual({ success: false, error: "Passwords do not match" });
    expect(navigationGateway.navigatedTo).toBeUndefined();
  });

  it("should fail when any field is empty", async () => {
    // Arrange
    const authGateway = new FakeAuthGateway(true);
    const navigationGateway = new FakeNavigationGateway();
    const handler = new RegisterCommandHandler(authGateway, navigationGateway);
    // Act
    const result = await handler.execute({
      name: "", email: "", password: "", confirmPassword: "", redirectTo: "/dashboard" });
    // Assert
    expect(result).toEqual({ success: false, error: "Please fill in all fields" });
    expect(navigationGateway.navigatedTo).toBeUndefined();
  });

  it("should fail when password is too short", async () => {
    // Arrange
    const authGateway = new FakeAuthGateway(true);
    const navigationGateway = new FakeNavigationGateway();
    const handler = new RegisterCommandHandler(authGateway, navigationGateway);
    // Act
    const result = await handler.execute({
      name: "Alex", email: "<EMAIL>", password: "123", confirmPassword: "123", redirectTo: "/dashboard" });
    // Assert
    expect(result).toEqual({ success: false, error: "Password must be at least 6 characters" });
    expect(navigationGateway.navigatedTo).toBeUndefined();
  });

  it("should fail when gateway rejects registration", async () => {
    // Arrange
    const authGateway = new FakeAuthGateway(false);
    const navigationGateway = new FakeNavigationGateway();
    const handler = new RegisterCommandHandler(authGateway, navigationGateway);
    // Act
    const result = await handler.execute({
      name: "Alex", email: "<EMAIL>", password: "password123", confirmPassword: "password123", redirectTo: "/dashboard" });
    // Assert
    expect(result).toEqual({ success: false, error: "Email already exists or registration failed" });
    expect(navigationGateway.navigatedTo).toBeUndefined();
  });
});

