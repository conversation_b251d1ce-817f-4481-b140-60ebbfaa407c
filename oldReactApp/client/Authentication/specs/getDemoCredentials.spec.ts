import { describe, it, expect } from 'vitest';
import { getDemoCredentialsQueryHandler } from '@/Authentication/application/queries/getDemoCredentials/getDemoCredentialsQueryHandler';

describe("When retrieving demo credentials", () => {
  it("should return alex's credentials when alex is selected", () => {
    // Arrange
    const handle = getDemoCredentialsQueryHandler();
    // Act
    const result = handle({ userType: 'alex' });
    // Assert
    expect(result).toEqual({ email: '<EMAIL>', password: 'password123' });
  });

  it("should return sarah's credentials when sarah is selected", () => {
    // Arrange
    const handle = getDemoCredentialsQueryHandler();
    // Act
    const result = handle({ userType: 'sarah' });
    // Assert
    expect(result).toEqual({ email: '<EMAIL>', password: 'password123' });
  });
});
