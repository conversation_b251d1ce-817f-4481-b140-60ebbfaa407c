import type { AuthGateway } from '@/Authentication/application/gateways/AuthGateway';

export class AuthContextAuthGateway implements AuthGateway {
  constructor(
    private readonly loginFn: (email: string, password: string) => Promise<boolean>,
    private readonly registerFn: (name: string, email: string, password: string) => Promise<boolean>,
  ) {}
  login(email: string, password: string): Promise<boolean> {
    return this.loginFn(email, password);
  }
  register(name: string, email: string, password: string): Promise<boolean> {
    return this.registerFn(name, email, password);
  }
}
