import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { RegisterCommandHandler } from '@/Authentication/application/commands/register/registerCommandHandler';
import { AuthContextAuthGateway } from '@/Authentication/infrastructure/gateways/AuthContextAuthGateway';
import { RouterNavigationGateway } from '@/Authentication/infrastructure/gateways/RouterNavigationGateway';

export function useRegister() {
  const { login, register } = useAuth();
  const navigate = useNavigate();
  const authGateway = new AuthContextAuthGateway(login, register);
  const navigationGateway = new RouterNavigationGateway(navigate);
  const handler = new RegisterCommandHandler(authGateway, navigationGateway);
  return (
    name: string,
    email: string,
    password: string,
    confirmPassword: string,
    redirectTo: string,
  ) => handler.execute({ name, email, password, confirmPassword, redirectTo });
}
