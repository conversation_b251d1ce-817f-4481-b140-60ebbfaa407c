import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { SignInCommandHandler } from '@/Authentication/application/commands/signIn/signInCommandHandler';
import { AuthContextAuthGateway } from '@/Authentication/infrastructure/gateways/AuthContextAuthGateway';
import { RouterNavigationGateway } from '@/Authentication/infrastructure/gateways/RouterNavigationGateway';

export function useSignIn() {
  const { login, register } = useAuth();
  const navigate = useNavigate();
  const authGateway = new AuthContextAuthGateway(login, register);
  const navigationGateway = new RouterNavigationGateway(navigate);
  const handler = new SignInCommandHandler(authGateway, navigationGateway);
  return (email: string, password: string, redirectTo: string) =>
    handler.execute({ email, password, redirectTo });
}
