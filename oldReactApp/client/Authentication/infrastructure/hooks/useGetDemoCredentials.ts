import { getDemoCredentialsQueryHandler } from '@/Authentication/application/queries/getDemoCredentials/getDemoCredentialsQueryHandler';
import type { DemoCredentials } from '@/Authentication/application/queries/getDemoCredentials/getDemoCredentialsQuery';

export function useGetDemoCredentials() {
  const handle = getDemoCredentialsQueryHandler();
  return (userType: 'alex' | 'sarah'): DemoCredentials => handle({ userType });
}
