import type { AuthGateway } from '@/Authentication/application/gateways/AuthGateway';
import type { NavigationGateway } from '@/Authentication/application/gateways/NavigationGateway';
import type { RegisterCommand } from './registerCommand';

export interface RegisterResult {
  success: boolean;
  error?: string;
}

export class RegisterCommandHandler {
  constructor(
    private readonly authGateway: AuthGateway,
    private readonly navigationGateway: NavigationGateway,
  ) {}

  async execute({
    name,
    email,
    password,
    confirmPassword,
    redirectTo,
  }: RegisterCommand): Promise<RegisterResult> {
    if (!name.trim() || !email.trim() || !password.trim()) {
      return { success: false, error: 'Please fill in all fields' };
    }
    if (password !== confirmPassword) {
      return { success: false, error: 'Passwords do not match' };
    }
    if (password.length < 6) {
      return { success: false, error: 'Password must be at least 6 characters' };
    }
    const success = await this.authGateway.register(name, email, password);
    if (success) {
      this.navigationGateway.navigateTo(redirectTo);
      return { success: true };
    }
    return { success: false, error: 'Email already exists or registration failed' };
  }
}
