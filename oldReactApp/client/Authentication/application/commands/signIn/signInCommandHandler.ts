import type { AuthGateway } from '@/Authentication/application/gateways/AuthGateway';
import type { NavigationGateway } from '@/Authentication/application/gateways/NavigationGateway';
import type { SignInCommand } from './signInCommand';

export class SignInCommandHandler {
  constructor(
    private readonly authGateway: AuthGateway,
    private readonly navigationGateway: NavigationGateway,
  ) {}

  async execute({ email, password, redirectTo }: SignInCommand) {
    const success = await this.authGateway.login(email, password);
    if (success) {
      this.navigationGateway.navigateTo(redirectTo);
    }
    return success;
  }
}
