# Use cases to migrate from pages

Page-level logic to move into dedicated use cases in src/client.

- **Index.tsx**
  - ~~Create session: redirect authenticated users to setup or guests to login with a return URL.~~
  - ~~Join session: validate session code and participant name before navigating to the planning room.~~
  - ~~Sign out: invoke the authentication context to terminate the current session.~~
- **Login.tsx**
  - ~~Authenticate user with email and password then redirect to the requested page.~~
  - ~~Prefill demo credentials for quick access to test accounts.~~
- **Register.tsx**
  - ~~Register a new account with name, email, and password while validating inputs and password confirmation.~~
- **NotFound.tsx**
  - ~~Log an attempt to visit a missing route for diagnostic purposes.~~
- **PlanningRoom.tsx**
  - ~~Vote by selecting estimation cards, reveal or reset votes, and change selections.~~
  - Manage stories: ~~add~~, ~~edit~~, ~~delete~~, ~~move to next~~, or ~~close with a final estimate~~.
  - ~~Copy session code~~, ~~switch estimation type~~, ~~compute voting statistics~~, and ~~end the session~~.
- **SessionHistory.tsx**
  - ~~Load sessions for the authenticated user, separate hosted from participated ones, and rejoin active sessions.~~
- **SessionSetup.tsx**
  - ~~Configure session name and estimation type, manage story list, and start the session as host.~~
- **SessionSetupStep1.tsx**
  - ~~Set up basic configuration (session name and estimation method) and store it for later steps.~~
- **SessionSetupStep2.tsx**
  - ~~Add, edit, or remove stories while persisting interim data before moving forward.~~
- **SessionSetupStep3.tsx**
  - ~~Finalize session creation, generate a session code, and offer clipboard copy before launching the planning room.~~
